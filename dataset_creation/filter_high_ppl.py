import json
import torch
import argparse
import numpy as np
from tqdm import tqdm
from transformers import AutoModelForCausalLM, AutoTokenizer
import math
import warnings
import fire

# Suppress a specific warning from the transformers library for cleaner output
warnings.filterwarnings(
    "ignore", message="The attention mask and the pad token id were not set.*"
)

# --- Core Inference and Perplexity Functions (Used by Step 1) ---


def get_perplexity(text, model, tokenizer, device):
    """Calculates the per-token perplexity of a given text."""
    inputs = tokenizer(text, return_tensors="pt").to(device)
    with torch.no_grad():
        outputs = model(**inputs, labels=inputs["input_ids"])
    loss_fct = torch.nn.CrossEntropyLoss(reduction="none")
    logits = outputs.logits
    shift_logits = logits[..., :-1, :].contiguous()
    shift_labels = inputs["input_ids"][..., 1:].contiguous()
    token_losses = loss_fct(
        shift_logits.view(-1, shift_logits.size(-1)), shift_labels.view(-1)
    )
    token_ppls = torch.exp(token_losses).tolist()
    return token_ppls


def run_inference_and_analyze(system_prompt, user_prompt, model, tokenizer, device):
    """Runs a single inference pass using the model's chat template and calculates perplexity."""
    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": user_prompt},
    ]
    prompt = tokenizer.apply_chat_template(
        messages, tokenize=False, add_generation_prompt=True
    )
    inputs = tokenizer(prompt, return_tensors="pt").to(device)
    input_ids_len = inputs["input_ids"].shape[1]
    output = model.generate(
        **inputs,
        max_new_tokens=60,
        pad_token_id=tokenizer.eos_token_id,
        do_sample=False,
    )
    generated_ids = output[0][input_ids_len:]
    generated_text = tokenizer.decode(generated_ids, skip_special_tokens=True)
    full_text_decoded = tokenizer.decode(output[0], skip_special_tokens=True)
    per_token_ppl = get_perplexity(full_text_decoded, model, tokenizer, device)
    generated_ppl = per_token_ppl[input_ids_len - 1 :]
    if not generated_ppl:
        return {"generated_text": generated_text, "max_ppl": 0, "avg_ppl": 0}
    return {
        "generated_text": generated_text,
        "max_ppl": max(generated_ppl),
        "avg_ppl": sum(generated_ppl) / len(generated_ppl),
    }


# --- Main Analysis Class for Fire ---


class SpikeAnalysis:
    """A tool to generate, summarize, and filter perplexity spike data for language models."""

    def _calculate_spike_score(self, max_ppl_baseline, max_ppl_pressure):
        """Internal helper to calculate the spike score."""
        if max_ppl_baseline > 0 and max_ppl_pressure > max_ppl_baseline:
            absolute_diff = max_ppl_pressure - max_ppl_baseline
            ratio = max_ppl_pressure / max_ppl_baseline
            return absolute_diff * math.log(ratio + 1)
        return 0.0

    def generate_ppl(
        self,
        input_file: str,
        output_file: str,
        model_name: str = "meta-llama/Llama-3.1-8B-Instruct",
    ):
        """
        Step 1: Runs dual inferences on an input file and saves results with PPL scores.
        """
        print("--- Step 1: Generating Perplexity Data ---")
        print(f"Loading model: {model_name}")
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        model = AutoModelForCausalLM.from_pretrained(
            model_name, torch_dtype=torch.bfloat16
        ).to(device)
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        print(f"✅ Model loaded on {device}.")
        neutral_system_prompt = "You are a helpful assistant."
        with open(output_file, "w") as out_f, open(input_file, "r") as in_f:
            lines = in_f.readlines()
            for line in tqdm(lines, desc="📊 Generating PPL data"):
                data = json.loads(line)
                baseline_result = run_inference_and_analyze(
                    neutral_system_prompt, data["user_prompt"], model, tokenizer, device
                )
                pressure_result = run_inference_and_analyze(
                    data["system_prompt"], data["user_prompt"], model, tokenizer, device
                )
                out_f.write(
                    json.dumps(
                        {
                            "original_data": data,
                            "baseline_result": baseline_result,
                            "pressure_result": pressure_result,
                        },
                        ensure_ascii=False,
                    )
                    + "\n"
                )
        print(f"\n✅ PPL data generation complete. Results saved to '{output_file}'.")

    def summarize_ppl(self, input_file: str, baseline_ppl_threshold: float = 1000.0):
        """
        Step 2: Analyzes the PPL data file and prints a statistical summary of spike scores.
        """
        print("\n--- Step 2: Summarizing Spike Scores ---")
        spike_scores = []
        with open(input_file, "r") as f:
            for line in tqdm(f, desc="📈 Calculating scores"):
                data = json.loads(line)
                max_ppl_baseline = data["baseline_result"]["max_ppl"]
                if max_ppl_baseline > baseline_ppl_threshold:
                    continue
                max_ppl_pressure = data["pressure_result"]["max_ppl"]
                score = self._calculate_spike_score(max_ppl_baseline, max_ppl_pressure)
                if score > 0:
                    spike_scores.append(score)

        if not spike_scores:
            print(
                "No valid spikes found with the given baseline threshold. Try increasing --baseline_ppl_threshold."
            )
            return

        print("\n--- Spike Score Statistical Summary ---")
        print(f"Total Valid Spikes Analyzed: {len(spike_scores)}")
        print(f"  Min Score:    {np.min(spike_scores):.2f}")
        print(f"  Max Score:    {np.max(spike_scores):.2f}")
        print(f"  Avg Score:    {np.mean(spike_scores):.2f}")
        print("\n--- Distribution (Percentiles) ---")
        print(f"  50th (Median): {np.percentile(spike_scores, 50):.2f}")
        print(f"  75th:          {np.percentile(spike_scores, 75):.2f}")
        print(f"  90th:          {np.percentile(spike_scores, 90):.2f}")
        print(f"  95th:          {np.percentile(spike_scores, 95):.2f}")
        print(f"  99th:          {np.percentile(spike_scores, 99):.2f}")
        print(
            "\n💡 Tip: Use these percentiles to choose a '--spike_threshold' for the 'filter_spikes' command."
        )
        print(
            "   For example, to get the top 5% of spikes, use the 95th percentile value as your threshold."
        )

    def filter_spikes(
        self,
        input_file: str,
        output_file: str,
        spike_threshold: float = 500.0,
        baseline_ppl_threshold: float = 1000.0,
    ):
        """
        Step 3: Filters the PPL data file and saves entries exceeding the spike threshold.
        """
        print("\n--- Step 3: Filtering by Spike Score ---")
        results_saved_count = 0
        with open(output_file, "w") as out_f, open(input_file, "r") as in_f:
            lines = in_f.readlines()
            for line in tqdm(lines, desc="🔎 Filtering for spikes"):
                data = json.loads(line)
                max_ppl_baseline = data["baseline_result"]["max_ppl"]
                if max_ppl_baseline > baseline_ppl_threshold:
                    continue
                max_ppl_pressure = data["pressure_result"]["max_ppl"]
                score = self._calculate_spike_score(max_ppl_baseline, max_ppl_pressure)
                if score >= spike_threshold:
                    data["spike_score"] = score
                    out_f.write(json.dumps(data, ensure_ascii=False) + "\n")
                    results_saved_count += 1
        print(
            f"✅ Filtering complete. Saved {results_saved_count} high-spike results to '{output_file}'."
        )


if __name__ == "__main__":
    fire.Fire(SpikeAnalysis)
