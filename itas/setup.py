from setuptools import setup, find_packages

setup(
    name="itas",
    version="0.1.0",
    author="<PERSON>",
    author_email="<EMAIL>",
    description="ITAS: Instruction-Truth Activation Steering - A comprehensive library for training and analyzing Sparse Auto-encoders (SAEs) on neural network representations",
    packages=find_packages(),
    long_description=open("README.md").read(),
    long_description_content_type="text/markdown",
    url="https://github.com/junkim100/itas",
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Software Development :: Libraries :: Python Modules",
        "Operating System :: OS Independent",
    ],
    python_requires=">=3.8",
    install_requires=[
        "torch>=2.0.0",
        "transformers>=4.30.0",
        "datasets>=2.0.0",
        "numpy>=1.21.0",
        "matplotlib>=3.5.0",
        "tqdm>=4.64.0",
        "wandb>=0.15.0",
        "einops>=0.6.0",
        "jaxtyping>=0.2.0",
        "huggingface-hub>=0.16.0",
    ],
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "black>=22.0.0",
            "isort>=5.10.0",
            "flake8>=4.0.0",
            "mypy>=0.950",
        ],
        "flash-attention": [
            "flash-attn>=2.0.0",
        ],
    },
    keywords="sparse autoencoder, representation engineering, interpretability, transformers, neural networks",
)
