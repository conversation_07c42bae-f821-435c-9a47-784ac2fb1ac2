#!/bin/bash

# OpenAI-Compatible SAE Steering Server Startup Script
# This script provides convenient presets for starting the server with different configurations

set -e

# Default values
MODEL_NAME="meta-llama/Llama-3.1-8B-Instruct"
ACTIVATIONS_DIR="itas/results/squad_v2/meta-llama-Llama-3.1-8B-Instruct_alpha1.0/activations"
TARGET_LAYER=16
STEERING_DIRECTION="truthful"
STEERING_ALPHA=1.0
DEVICE="auto"
PORT=8000
HOST="0.0.0.0"
LOG_LEVEL="INFO"

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to show usage
show_usage() {
    cat << EOF
OpenAI-Compatible SAE Steering Server Startup Script

Usage: $0 [PRESET] [OPTIONS]

PRESETS:
  default       Start with default settings (truthful steering, layer 16)
  scheming      Start with scheming steering enabled
  debug         Start with debug logging enabled
  cpu           Start with CPU-only mode
  custom        Start with custom parameters (use with options below)

OPTIONS:
  --model MODEL_NAME              HuggingFace model name
  --activations DIR               Directory containing activation files
  --layer LAYER                   Target layer for steering (default: 16)
  --direction DIRECTION           Steering direction: truthful|scheming (default: truthful)
  --alpha ALPHA                   Steering strength (default: 1.0)
  --device DEVICE                 Device: auto|cuda|cpu (default: auto)
  --port PORT                     Server port (default: 8000)
  --host HOST                     Server host (default: 0.0.0.0)
  --log-level LEVEL               Log level: DEBUG|INFO|WARNING|ERROR (default: INFO)
  --help                          Show this help message

EXAMPLES:
  # Start with default settings
  $0 default

  # Start with scheming steering
  $0 scheming

  # Start with custom layer and stronger steering
  $0 custom --layer 20 --alpha 2.0 --direction truthful

  # Start in debug mode on a specific port
  $0 debug --port 8080

  # Start with CPU-only mode
  $0 cpu

EOF
}

# Function to check if server is already running
check_server_running() {
    if curl -s "http://localhost:$PORT/health" > /dev/null 2>&1; then
        print_warning "Server appears to be already running on port $PORT"
        echo "You can check status with: curl http://localhost:$PORT/status"
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

# Function to validate activations directory
validate_activations_dir() {
    if [[ ! -d "$ACTIVATIONS_DIR" ]]; then
        print_error "Activations directory not found: $ACTIVATIONS_DIR"
        print_info "Please ensure you have generated functional activations first."
        print_info "You can generate them using:"
        print_info "  python scripts/steered_inference.py create_activations --model_name $MODEL_NAME"
        exit 1
    fi

    # Check for activation files
    if ! ls "$ACTIVATIONS_DIR"/functional_activations_*.pt > /dev/null 2>&1; then
        print_error "No functional activation files found in: $ACTIVATIONS_DIR"
        print_info "Expected files matching pattern: functional_activations_*.pt"
        exit 1
    fi

    print_success "Found activation files in: $ACTIVATIONS_DIR"
}

# Function to check dependencies
check_dependencies() {
    print_info "Checking dependencies..."

    # Check if Python is available
    if ! command -v python &> /dev/null; then
        print_error "Python not found. Please install Python 3.8 or later."
        exit 1
    fi

    # Check if required packages are installed
    python -c "import fastapi, uvicorn, torch, transformers" 2>/dev/null || {
        print_error "Required packages not installed."
        print_info "Please install dependencies with: pip install -r requirements_server.txt"
        exit 1
    }

    print_success "Dependencies check passed"
}

# Parse preset
PRESET=${1:-default}

case $PRESET in
    default)
        print_info "Using default preset (truthful steering, layer 16)"
        ;;
    scheming)
        print_info "Using scheming preset"
        STEERING_DIRECTION="scheming"
        STEERING_ALPHA=1.5
        ;;
    debug)
        print_info "Using debug preset"
        LOG_LEVEL="DEBUG"
        ;;
    cpu)
        print_info "Using CPU-only preset"
        DEVICE="cpu"
        ;;
    custom)
        print_info "Using custom preset - configure with options"
        ;;
    --help|-h|help)
        show_usage
        exit 0
        ;;
    *)
        print_error "Unknown preset: $PRESET"
        show_usage
        exit 1
        ;;
esac

# Parse additional options
shift
while [[ $# -gt 0 ]]; do
    case $1 in
        --model)
            MODEL_NAME="$2"
            shift 2
            ;;
        --activations)
            ACTIVATIONS_DIR="$2"
            shift 2
            ;;
        --layer)
            TARGET_LAYER="$2"
            shift 2
            ;;
        --direction)
            STEERING_DIRECTION="$2"
            shift 2
            ;;
        --alpha)
            STEERING_ALPHA="$2"
            shift 2
            ;;
        --device)
            DEVICE="$2"
            shift 2
            ;;
        --port)
            PORT="$2"
            shift 2
            ;;
        --host)
            HOST="$2"
            shift 2
            ;;
        --log-level)
            LOG_LEVEL="$2"
            shift 2
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validation
check_dependencies
validate_activations_dir
check_server_running

# Print configuration
echo
print_info "Starting OpenAI-Compatible SAE Steering Server"
echo "Configuration:"
echo "  Model: $MODEL_NAME"
echo "  Activations: $ACTIVATIONS_DIR"
echo "  Target Layer: $TARGET_LAYER"
echo "  Steering: $STEERING_DIRECTION (α=$STEERING_ALPHA)"
echo "  Device: $DEVICE"
echo "  Server: $HOST:$PORT"
echo "  Log Level: $LOG_LEVEL"
echo

# Start the server
print_info "Starting server..."

# Find the correct path to openai_server.py (should be in parent directory)
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SERVER_SCRIPT="$SCRIPT_DIR/../openai_server.py"

if [[ ! -f "$SERVER_SCRIPT" ]]; then
    print_error "openai_server.py not found at: $SERVER_SCRIPT"
    print_info "Please ensure openai_server.py exists in the itas directory"
    exit 1
fi

exec python "$SERVER_SCRIPT" \
    --model_name "$MODEL_NAME" \
    --activations_dir "$ACTIVATIONS_DIR" \
    --target_layer "$TARGET_LAYER" \
    --steering_direction "$STEERING_DIRECTION" \
    --steering_alpha "$STEERING_ALPHA" \
    --device "$DEVICE" \
    --port "$PORT" \
    --host "$HOST" \
    --log_level "$LOG_LEVEL"
