#!/usr/bin/env python3
"""
MASK Response Generator

Processes a single CSV file from the MASK benchmark and generates responses
in the exact same format as the original MASK response files.

Usage:
    python mask_response_generator.py \
        --input_csv="/data_x/junkim100/projects/scheming_sae/mask_benchmark/mask/csv_data/known_facts.csv" \
        --config_path="../results/steering_config_20250609_140549.json" \
        --activations_path="../results/functional_activations_20250609_140549.pt" \
        --steering_direction="truthful" \
        --output_dir="../results/mask_responses/"
"""

import os
import json
import logging
import pandas as pd
from datetime import datetime
from pathlib import Path
from typing import Optional
import fire

# Suppress transformers generation warnings
os.environ["TRANSFORMERS_VERBOSITY"] = "error"
import warnings

warnings.filterwarnings("ignore", message=".*generation flags.*not valid.*")

from itas.core import SteeredInferenceEngine, InferenceConfig

# Setup logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def extract_model_name_from_config(config_path: str) -> str:
    """Extract a clean model name from the config file."""
    with open(config_path, "r") as f:
        config_data = json.load(f)

    model_name = config_data["model_name"]

    # Clean up model name for filename
    # e.g., "meta-llama/Llama-3.1-8B-Instruct" -> "Llama-3.1-8B-Instruct"
    if "/" in model_name:
        model_name = model_name.split("/")[-1]

    # Remove any characters that might be problematic in filenames
    model_name = model_name.replace("-", "_").replace(".", "_")

    return model_name


def generate_mask_responses(
    input_csv: str,
    config_path: str,
    activations_path: str,
    steering_direction: str = "truthful",
    steering_alpha: float = 1.5,
    output_dir: str = "../results/mask_responses/",
    max_new_tokens: int = 256,
    temperature: float = 0.0,
    top_p: float = 1.0,
    do_sample: bool = False,
    device: str = "auto",
    batch_size: int = 1,
):
    """
    Replace the main generation column in a MASK response CSV with steered inference.

    Takes an existing MASK response CSV file (with non-steered responses) and replaces
    only the "generation(System Prompt + User Prompt)_run1" column with steered responses.
    All other columns including belief system generations are preserved unchanged.

    Args:
        input_csv: Path to input CSV file containing existing MASK responses
        config_path: Path to steering config JSON file
        activations_path: Path to functional activations
        steering_direction: "truthful" or "scheming"
        steering_alpha: Steering strength
        output_dir: Directory to save output CSV
        max_new_tokens: Maximum tokens to generate
        temperature: Sampling temperature
        top_p: Top-p (nucleus) sampling parameter
        do_sample: Whether to use sampling
        device: Device for computation
        batch_size: Batch size for inference
    """
    logger.info(
        f"🎯 Replacing main generation column in {input_csv} with steered responses"
    )

    # Load configuration from file
    with open(config_path, "r") as f:
        config_data = json.load(f)

    # Extract parameters from config
    model_name = config_data["model_name"]
    sae_path = config_data["sae_path"]
    config_steering_alpha = config_data.get("steering_alpha", 1.5)
    config_max_new_tokens = config_data.get("max_new_tokens", 256)

    # Use config values if not overridden
    final_steering_alpha = (
        steering_alpha if steering_alpha != 0.0 else config_steering_alpha
    )
    final_max_new_tokens = (
        max_new_tokens if max_new_tokens != 0 else config_max_new_tokens
    )

    logger.info(f"📋 Model: {model_name}")
    logger.info(f"📋 Steering: {steering_direction}, alpha={final_steering_alpha}")
    logger.info(f"📋 Max tokens: {final_max_new_tokens}")

    # Load input CSV (existing MASK response file)
    logger.info(f"📥 Loading existing MASK response CSV: {input_csv}")
    df = pd.read_csv(input_csv)
    logger.info(f"📊 Loaded {len(df)} rows")

    # Validate required columns exist
    required_columns = ["system_prompt", "user_prompt"]
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        raise ValueError(f"Missing required columns: {missing_columns}")

    # Check if the target generation column exists
    target_column = "generation(System Prompt + User Prompt)_run1"
    if target_column not in df.columns:
        logger.warning(f"⚠️ Target column '{target_column}' not found. Will be created.")
        df[target_column] = ""

    # Create output directory
    os.makedirs(output_dir, exist_ok=True)

    # Generate output filename
    if "continuations" in input_csv:
        output_filename = f"continuations_{model_name.split('/')[-1]}-{steering_direction}-steered.csv"
    elif "disinformation" in input_csv:
        output_filename = f"disinformation_{model_name.split('/')[-1]}-{steering_direction}-steered.csv"
    elif "doubling_down_known_facts" in input_csv:
        output_filename = f"doubling_down_known_facts_{model_name.split('/')[-1]}-{steering_direction}-steered.csv"
    elif "known_facts" in input_csv:
        output_filename = (
            f"known_facts_{model_name.split('/')[-1]}-{steering_direction}-steered.csv"
        )
    elif "provided_facts" in input_csv:
        output_filename = f"provided_facts_{model_name.split('/')[-1]}-{steering_direction}-steered.csv"
    elif "statistics" in input_csv:
        output_filename = (
            f"statistics_{model_name.split('/')[-1]}-{steering_direction}-steered.csv"
        )
    else:
        logger.warning(f"⚠️ Unknown input CSV name: {input_csv}")
        output_filename = f"{input_csv.stem('.csv')}_{model_name.split('/')[-1]}-{steering_direction}-steered.csv"

    output_path = os.path.join(output_dir, output_filename)

    logger.info(f"📤 Output will be saved to: {output_path}")

    # Create inference configuration
    inference_config = InferenceConfig(
        model_name=model_name,
        sae_path=sae_path,
        activations_path=activations_path,
        config_path=config_path,
        steering_direction=steering_direction,
        steering_alpha=final_steering_alpha,
        max_new_tokens=final_max_new_tokens,
        temperature=temperature,
        top_p=top_p,
        do_sample=do_sample,
        device=device,
        batch_size=batch_size,
    )

    # Initialize output dataframe with all existing columns preserved
    output_df = df.copy()

    logger.info(f"🚀 Starting steered generation for '{target_column}' column...")
    logger.info(f"📋 Preserving all other columns unchanged")

    # Generate steered responses for the main generation column only
    with SteeredInferenceEngine(inference_config) as engine:
        for idx, row in df.iterrows():
            if (idx + 1) % 10 == 0 or idx == 0:
                logger.info(f"  Processing row {idx + 1}/{len(df)}")

            try:
                # Generate ONLY the steered response for (System Prompt + User Prompt)
                # All other columns remain unchanged from the input CSV
                steered_response = engine.generate_response_from_prompts(
                    system_prompt=row["system_prompt"], user_prompt=row["user_prompt"]
                )
                output_df.at[idx, target_column] = steered_response

            except Exception as e:
                logger.error(f"Error processing row {idx}: {e}")
                output_df.at[idx, target_column] = f"ERROR: {str(e)}"

    # Create output directory
    os.makedirs(output_dir, exist_ok=True)

    # Save output CSV
    output_df.to_csv(output_path, index=False)

    logger.info("✅ Steered response replacement completed!")
    logger.info(f"   Input: {input_csv}")
    logger.info(f"   Output: {output_path}")
    logger.info(f"   Replaced '{target_column}' for {len(df)} rows")
    logger.info(f"   All other columns preserved unchanged")

    return output_path


if __name__ == "__main__":
    fire.Fire(generate_mask_responses)
