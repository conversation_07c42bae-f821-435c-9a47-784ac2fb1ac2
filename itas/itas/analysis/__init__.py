"""
Analysis and evaluation tools for ITAS.

This module provides comprehensive tools for analyzing SAE features,
extracting functions, and performing knowledge selection steering.
"""

from .activation_analyzer import ActivationAnalyzer, ActivationStats
from .evaluation import SAEEvaluator, SAEEvaluationResult, InterventionEvaluationResult
from .visualization import SAEVisualizer

__all__ = [
    # Main classes
    "ActivationAnalyzer",
    "SAEEvaluator",
    "SAEVisualizer",
    # Result classes
    "ActivationStats",
    "SAEEvaluationResult",
    "InterventionEvaluationResult",
    "ClassificationResult",
    # Classifier components
    "LogisticRegressionClassifier",
    "TrainingConfig",
    # Utility functions
    "create_classifier_from_sae",
    "train_conflict_classifier",
]
