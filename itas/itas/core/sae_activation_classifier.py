"""
SAE Activation Classifier for Behavioral Conflict Detection.

This module provides a classifier that uses SAE (Sparse Autoencoder) activation
values to distinguish between different types of behavior inputs, specifically
designed to detect conflicts between different behavioral patterns.
"""

import logging
from typing import Dict, Any, List, Optional, Tuple, Union
from dataclasses import dataclass
from pathlib import Path

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
from sklearn.model_selection import StratifiedKFold, train_test_split
from sklearn.metrics import (
    accuracy_score,
    f1_score,
    precision_score,
    recall_score,
    roc_auc_score,
    precision_recall_curve,
    auc,
)
import numpy as np

from ..core.sae import SAE
from ..core.config import TrainingConfig

logger = logging.getLogger(__name__)


@dataclass
class ClassificationResult:
    """Results from SAE activation classification."""

    accuracy: float
    auc: float
    auprc: float
    precision: float
    recall: float
    f1_score: float
    predictions: List[float]
    labels: List[int]
    feature_importance: Optional[torch.Tensor] = None
    metadata: Dict[str, Any] = None


class LogisticRegressionClassifier(nn.Module):
    """
    Logistic regression classifier for SAE activations.

    This is an enhanced version of the existing LogisticRegression class
    with additional features for SAE activation classification.
    """

    def __init__(
        self, input_dim: int, use_bias: bool = True, dropout_rate: float = 0.0
    ):
        """
        Initialize logistic regression classifier.

        Args:
            input_dim: Dimension of input features (SAE feature dimension)
            use_bias: Whether to use bias term
            dropout_rate: Dropout rate for regularization
        """
        super().__init__()
        self.linear = nn.Linear(input_dim, 1, bias=use_bias)
        self.dropout = nn.Dropout(dropout_rate) if dropout_rate > 0 else nn.Identity()

        # Initialize weights
        nn.init.xavier_uniform_(self.linear.weight)
        if use_bias:
            nn.init.zeros_(self.linear.bias)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Forward pass."""
        x = self.dropout(x)
        return torch.sigmoid(self.linear(x)).squeeze(1)

    @torch.inference_mode()
    def predict(self, x: torch.Tensor) -> torch.Tensor:
        """Make predictions."""
        if not isinstance(x, torch.Tensor):
            x = torch.tensor(x, dtype=torch.float32)
        x = x.to(self.linear.weight.device)
        return self.forward(x)

    def get_feature_importance(self) -> torch.Tensor:
        """Get feature importance (absolute weights)."""
        return torch.abs(self.linear.weight.squeeze())


class SAEActivationClassifier:
    """
    Classifier for detecting behavioral conflicts using SAE activations.

    This classifier takes SAE feature activations as input and predicts
    whether the activation pattern corresponds to a conflict scenario.
    """

    def __init__(
        self,
        sae: SAE,
        config: Optional[TrainingConfig] = None,
        device: Optional[str] = None,
    ):
        """
        Initialize SAE activation classifier.

        Args:
            sae: Trained SAE model for feature extraction
            config: Training configuration
            device: Device for computation
        """
        self.sae = sae
        self.config = config or TrainingConfig()
        self.device = device or self.config.device

        # Move SAE to device
        self.sae = self.sae.to(self.device)
        self.sae.eval()

        # Classifier will be initialized during training
        self.classifier = None
        self.training_history = []

        logger.info(f"Initialized SAE activation classifier on {self.device}")

    def extract_sae_features(
        self, activations: torch.Tensor, normalize: bool = True
    ) -> torch.Tensor:
        """
        Extract SAE features from raw activations.

        Args:
            activations: Raw activations [batch_size, hidden_dim]
            normalize: Whether to normalize features

        Returns:
            SAE feature activations [batch_size, sae_dim]
        """
        activations = activations.to(self.device)

        with torch.no_grad():
            # Get SAE features
            sae_output = self.sae(activations)

            # Extract feature activations
            if hasattr(sae_output, "feature_acts"):
                features = sae_output.feature_acts
            elif hasattr(sae_output, "latent_acts"):
                features = sae_output.latent_acts
            elif hasattr(sae_output, "h"):
                features = sae_output.h
            else:
                raise AttributeError(
                    f"Could not find feature activations in SAE output. "
                    f"Available attributes: {dir(sae_output)}"
                )

            # Optional normalization
            if normalize:
                features = features / (features.norm(dim=1, keepdim=True) + 1e-8)

        return features

    def prepare_training_data(
        self,
        conflict_activations: torch.Tensor,
        no_conflict_activations: torch.Tensor,
        extract_features: bool = True,
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Prepare training data from activation tensors.

        Args:
            conflict_activations: Activations from conflict scenarios [n_conflict, hidden_dim]
            no_conflict_activations: Activations from no-conflict scenarios [n_no_conflict, hidden_dim]
            extract_features: Whether to extract SAE features or use raw activations

        Returns:
            Tuple of (features, labels) tensors
        """
        logger.info(
            f"Preparing training data: {conflict_activations.shape[0]} conflict, "
            f"{no_conflict_activations.shape[0]} no-conflict samples"
        )

        # Extract features if requested
        if extract_features:
            conflict_features = self.extract_sae_features(conflict_activations)
            no_conflict_features = self.extract_sae_features(no_conflict_activations)
        else:
            conflict_features = conflict_activations.to(self.device)
            no_conflict_features = no_conflict_activations.to(self.device)

        # Combine features and create labels
        features = torch.cat([conflict_features, no_conflict_features], dim=0)
        labels = torch.cat(
            [
                torch.ones(conflict_features.shape[0]),  # 1 for conflict
                torch.zeros(no_conflict_features.shape[0]),  # 0 for no conflict
            ]
        ).to(self.device)

        logger.info(
            f"Prepared features shape: {features.shape}, labels shape: {labels.shape}"
        )
        return features, labels

    def train(
        self,
        conflict_activations: torch.Tensor,
        no_conflict_activations: torch.Tensor,
        validation_data: Optional[Tuple[torch.Tensor, torch.Tensor]] = None,
        extract_features: bool = True,
    ) -> ClassificationResult:
        """
        Train the classifier on SAE activations.

        Args:
            conflict_activations: Activations from conflict scenarios
            no_conflict_activations: Activations from no-conflict scenarios
            validation_data: Optional validation data (features, labels)
            extract_features: Whether to extract SAE features

        Returns:
            Classification results on validation/test data
        """
        logger.info("Starting classifier training")

        # Prepare training data
        features, labels = self.prepare_training_data(
            conflict_activations, no_conflict_activations, extract_features
        )

        # Split into train/validation if no validation data provided
        if validation_data is None:
            train_features, val_features, train_labels, val_labels = train_test_split(
                features.detach().cpu().numpy(),
                labels.cpu().numpy(),
                test_size=self.config.validation_split,
                stratify=labels.cpu().numpy(),
                random_state=self.config.random_state,
            )
            train_features = torch.tensor(train_features, dtype=torch.float32).to(
                self.device
            )
            val_features = torch.tensor(val_features, dtype=torch.float32).to(
                self.device
            )
            train_labels = torch.tensor(train_labels, dtype=torch.float32).to(
                self.device
            )
            val_labels = torch.tensor(val_labels, dtype=torch.float32).to(self.device)
        else:
            train_features, train_labels = features, labels
            val_features, val_labels = validation_data
            val_features = val_features.to(self.device)
            val_labels = val_labels.to(self.device)

        # Initialize classifier
        input_dim = train_features.shape[1]
        self.classifier = LogisticRegressionClassifier(input_dim).to(self.device)

        # Setup training
        optimizer = optim.Adam(
            self.classifier.parameters(), lr=self.config.learning_rate
        )
        criterion = nn.BCELoss()
        scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=1, gamma=0.95)

        # Training loop
        best_val_score = -1
        patience_counter = 0
        best_model_state = None

        train_dataset = TensorDataset(train_features, train_labels)
        train_loader = DataLoader(
            train_dataset, batch_size=self.config.batch_size, shuffle=True
        )

        for epoch in range(self.config.num_epochs):
            # Training phase
            self.classifier.train()
            epoch_loss = 0.0

            for batch_features, batch_labels in train_loader:
                optimizer.zero_grad()

                outputs = self.classifier(batch_features)
                loss = criterion(outputs, batch_labels)

                # Add L1 regularization
                l1_penalty = 0
                for param in self.classifier.parameters():
                    l1_penalty += torch.sum(torch.abs(param))
                loss += self.config.l1_coefficient * l1_penalty

                loss.backward()
                optimizer.step()
                epoch_loss += loss.item()

            scheduler.step()

            # Validation phase
            if epoch % 10 == 0 or epoch == self.config.num_epochs - 1:
                val_result = self._evaluate_model(val_features, val_labels)
                val_score = val_result.auc

                logger.info(
                    f"Epoch {epoch}: Loss = {epoch_loss/len(train_loader):.6f}, "
                    f"Val AUC = {val_score:.4f}"
                )

                # Early stopping
                if val_score > best_val_score:
                    best_val_score = val_score
                    patience_counter = 0
                    best_model_state = self.classifier.state_dict().copy()
                else:
                    patience_counter += 1

                if patience_counter >= self.config.early_stopping_patience:
                    logger.info(f"Early stopping at epoch {epoch}")
                    break

        # Load best model
        if best_model_state is not None:
            self.classifier.load_state_dict(best_model_state)

        # Final evaluation
        final_result = self._evaluate_model(val_features, val_labels)
        final_result.feature_importance = self.classifier.get_feature_importance()

        logger.info(f"Training completed. Final AUC: {final_result.auc:.4f}")
        return final_result

    def _evaluate_model(
        self, features: torch.Tensor, labels: torch.Tensor
    ) -> ClassificationResult:
        """Evaluate model performance."""
        self.classifier.eval()

        with torch.no_grad():
            predictions = self.classifier(features).cpu().numpy()
            labels_np = labels.cpu().numpy()

            # Binary predictions
            binary_preds = (predictions > 0.5).astype(int)

            # Calculate metrics
            accuracy = accuracy_score(labels_np, binary_preds)
            precision = precision_score(labels_np, binary_preds, zero_division=0)
            recall = recall_score(labels_np, binary_preds, zero_division=0)
            f1 = f1_score(labels_np, binary_preds, zero_division=0)

            # AUC metrics
            try:
                auc_score = roc_auc_score(labels_np, predictions)
            except ValueError:
                auc_score = 0.0

            try:
                precision_curve, recall_curve, _ = precision_recall_curve(
                    labels_np, predictions
                )
                auprc = auc(recall_curve, precision_curve)
            except ValueError:
                auprc = 0.0

        return ClassificationResult(
            accuracy=accuracy,
            auc=auc_score,
            auprc=auprc,
            precision=precision,
            recall=recall,
            f1_score=f1,
            predictions=predictions.tolist(),
            labels=labels_np.tolist(),
        )

    def evaluate(
        self,
        test_activations: torch.Tensor,
        test_labels: torch.Tensor,
        extract_features: bool = True,
    ) -> ClassificationResult:
        """
        Evaluate classifier on test data.

        Args:
            test_activations: Test activations
            test_labels: Test labels (0 for no conflict, 1 for conflict)
            extract_features: Whether to extract SAE features

        Returns:
            Classification results
        """
        if self.classifier is None:
            raise ValueError("Classifier not trained. Call train() first.")

        # Extract features if needed
        if extract_features:
            test_features = self.extract_sae_features(test_activations)
        else:
            test_features = test_activations.to(self.device)

        test_labels = test_labels.to(self.device)

        result = self._evaluate_model(test_features, test_labels)
        result.feature_importance = self.classifier.get_feature_importance()

        return result

    def cross_validate(
        self,
        conflict_activations: torch.Tensor,
        no_conflict_activations: torch.Tensor,
        n_folds: int = 5,
        extract_features: bool = True,
    ) -> List[ClassificationResult]:
        """
        Perform cross-validation.

        Args:
            conflict_activations: Conflict activations
            no_conflict_activations: No-conflict activations
            n_folds: Number of CV folds
            extract_features: Whether to extract SAE features

        Returns:
            List of results for each fold
        """
        logger.info(f"Starting {n_folds}-fold cross-validation")

        # Prepare data
        features, labels = self.prepare_training_data(
            conflict_activations, no_conflict_activations, extract_features
        )

        # Convert to numpy for sklearn
        features_np = features.cpu().numpy()
        labels_np = labels.cpu().numpy()

        # Cross-validation
        skf = StratifiedKFold(
            n_splits=n_folds, shuffle=True, random_state=self.config.random_state
        )
        results = []

        for fold, (train_idx, val_idx) in enumerate(skf.split(features_np, labels_np)):
            logger.info(f"Training fold {fold + 1}/{n_folds}")

            # Split data
            train_features = torch.tensor(
                features_np[train_idx], dtype=torch.float32
            ).to(self.device)
            val_features = torch.tensor(features_np[val_idx], dtype=torch.float32).to(
                self.device
            )
            train_labels = torch.tensor(labels_np[train_idx], dtype=torch.float32).to(
                self.device
            )
            val_labels = torch.tensor(labels_np[val_idx], dtype=torch.float32).to(
                self.device
            )

            # Train classifier for this fold
            input_dim = train_features.shape[1]
            fold_classifier = LogisticRegressionClassifier(input_dim).to(self.device)

            # Training setup
            optimizer = optim.Adam(
                fold_classifier.parameters(), lr=self.config.learning_rate
            )
            criterion = nn.BCELoss()

            # Training loop (simplified for CV)
            train_dataset = TensorDataset(train_features, train_labels)
            train_loader = DataLoader(
                train_dataset, batch_size=self.config.batch_size, shuffle=True
            )

            for _ in range(self.config.num_epochs):
                fold_classifier.train()
                for batch_features, batch_labels in train_loader:
                    optimizer.zero_grad()
                    outputs = fold_classifier(batch_features)
                    loss = criterion(outputs, batch_labels)

                    # L1 regularization
                    l1_penalty = 0
                    for param in fold_classifier.parameters():
                        l1_penalty += torch.sum(torch.abs(param))
                    loss += self.config.l1_coefficient * l1_penalty

                    loss.backward()
                    optimizer.step()

            # Evaluate fold
            fold_classifier.eval()
            with torch.no_grad():
                predictions = fold_classifier(val_features).cpu().numpy()
                labels_fold = val_labels.cpu().numpy()

                binary_preds = (predictions > 0.5).astype(int)

                accuracy = accuracy_score(labels_fold, binary_preds)
                precision = precision_score(labels_fold, binary_preds, zero_division=0)
                recall = recall_score(labels_fold, binary_preds, zero_division=0)
                f1 = f1_score(labels_fold, binary_preds, zero_division=0)

                try:
                    auc_score = roc_auc_score(labels_fold, predictions)
                except ValueError:
                    auc_score = 0.0

                try:
                    precision_curve, recall_curve, _ = precision_recall_curve(
                        labels_fold, predictions
                    )
                    auprc = auc(recall_curve, precision_curve)
                except ValueError:
                    auprc = 0.0

                fold_result = ClassificationResult(
                    accuracy=accuracy,
                    auc=auc_score,
                    auprc=auprc,
                    precision=precision,
                    recall=recall,
                    f1_score=f1,
                    predictions=predictions.tolist(),
                    labels=labels_fold.tolist(),
                    feature_importance=fold_classifier.get_feature_importance(),
                    metadata={"fold": fold},
                )

                results.append(fold_result)
                logger.info(
                    f"Fold {fold + 1} - AUC: {auc_score:.4f}, Accuracy: {accuracy:.4f}"
                )

        return results

    def get_feature_importance(self, top_k: Optional[int] = None) -> Dict[str, Any]:
        """
        Get feature importance from trained classifier.

        Args:
            top_k: Number of top features to return

        Returns:
            Dictionary with feature importance information
        """
        if self.classifier is None:
            raise ValueError("Classifier not trained. Call train() first.")

        importance = self.classifier.get_feature_importance()

        # Get top features
        if top_k is not None:
            top_indices = torch.topk(importance, min(top_k, len(importance))).indices
            top_values = importance[top_indices]
        else:
            top_indices = torch.argsort(importance, descending=True)
            top_values = importance[top_indices]

        return {
            "feature_importance": importance.detach().cpu().numpy(),
            "top_features": top_indices.detach().cpu().numpy(),
            "top_values": top_values.detach().cpu().numpy(),
            "mean_importance": importance.mean().item(),
            "std_importance": importance.std().item(),
            "sparsity": (importance == 0).float().mean().item(),
        }

    def save_model(self, path: Union[str, Path]) -> None:
        """Save trained classifier."""
        if self.classifier is None:
            raise ValueError("No trained classifier to save.")

        save_dict = {
            "classifier_state_dict": self.classifier.state_dict(),
            "config": self.config,
            "input_dim": self.classifier.linear.in_features,
            "training_history": self.training_history,
        }

        torch.save(save_dict, path)
        logger.info(f"Saved classifier to {path}")

    def load_model(self, path: Union[str, Path]) -> None:
        """Load trained classifier."""
        checkpoint = torch.load(path, map_location=self.device)

        # Initialize classifier with correct dimensions
        input_dim = checkpoint["input_dim"]
        self.classifier = LogisticRegressionClassifier(input_dim).to(self.device)
        self.classifier.load_state_dict(checkpoint["classifier_state_dict"])

        # Load config and history
        self.config = checkpoint.get("config", self.config)
        self.training_history = checkpoint.get("training_history", [])

        logger.info(f"Loaded classifier from {path}")

    def predict_from_activations(
        self,
        activations: torch.Tensor,
        extract_features: bool = True,
        return_probabilities: bool = True,
    ) -> Union[torch.Tensor, Tuple[torch.Tensor, torch.Tensor]]:
        """
        Make predictions from raw activations.

        Args:
            activations: Input activations
            extract_features: Whether to extract SAE features
            return_probabilities: Whether to return probabilities or binary predictions

        Returns:
            Predictions (and probabilities if requested)
        """
        if self.classifier is None:
            raise ValueError("Classifier not trained. Call train() first.")

        # Extract features if needed
        if extract_features:
            features = self.extract_sae_features(activations)
        else:
            features = activations.to(self.device)

        # Make predictions
        self.classifier.eval()
        with torch.no_grad():
            probabilities = self.classifier(features)
            binary_predictions = (probabilities > 0.5).float()

        if return_probabilities:
            return binary_predictions, probabilities
        else:
            return binary_predictions

    @staticmethod
    def summarize_cv_results(
        cv_results: List[ClassificationResult],
    ) -> Dict[str, float]:
        """
        Summarize cross-validation results.

        Args:
            cv_results: List of CV fold results

        Returns:
            Summary statistics
        """
        metrics = ["accuracy", "auc", "auprc", "precision", "recall", "f1_score"]
        summary = {}

        for metric in metrics:
            values = [getattr(result, metric) for result in cv_results]
            summary[f"{metric}_mean"] = np.mean(values)
            summary[f"{metric}_std"] = np.std(values)
            summary[f"{metric}_min"] = np.min(values)
            summary[f"{metric}_max"] = np.max(values)

        return summary


# Utility functions for easy usage
def create_classifier_from_sae(
    sae_path: Union[str, Path],
    device: Optional[str] = None,
    config: Optional[TrainingConfig] = None,
) -> SAEActivationClassifier:
    """
    Create classifier from saved SAE model.

    Args:
        sae_path: Path to saved SAE model
        device: Device for computation
        config: Training configuration

    Returns:
        Initialized classifier
    """
    # Load SAE
    sae = SAE.load(sae_path, device=device)

    # Create classifier
    classifier = SAEActivationClassifier(sae, config, device)

    return classifier


def train_conflict_classifier(
    sae: SAE,
    conflict_activations: torch.Tensor,
    no_conflict_activations: torch.Tensor,
    config: Optional[TrainingConfig] = None,
    cross_validate: bool = True,
    n_folds: int = 5,
) -> Tuple[
    SAEActivationClassifier, Union[ClassificationResult, List[ClassificationResult]]
]:
    """
    Train a conflict classifier with optional cross-validation.

    Args:
        sae: Trained SAE model
        conflict_activations: Activations from conflict scenarios
        no_conflict_activations: Activations from no-conflict scenarios
        config: Training configuration
        cross_validate: Whether to perform cross-validation
        n_folds: Number of CV folds

    Returns:
        Tuple of (trained classifier, results)
    """
    classifier = SAEActivationClassifier(sae, config)

    if cross_validate:
        results = classifier.cross_validate(
            conflict_activations, no_conflict_activations, n_folds
        )
        # Train final model on all data
        classifier.train(conflict_activations, no_conflict_activations)
        return classifier, results
    else:
        result = classifier.train(conflict_activations, no_conflict_activations)
        return classifier, result
