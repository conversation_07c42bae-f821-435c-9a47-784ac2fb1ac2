"""
Improved Sparse Auto-Encoder (SAE) implementation.

This module provides a unified SAE implementation that works with any
transformer model architecture and supports multiple SAE variants.
"""

import logging
import os
import json
from pathlib import Path
from typing import Optional, Dict, Any, Tuple, Union, Literal, List
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch import Tensor
from dataclasses import dataclass
import math

# Import HuggingFace utilities for loading from hub
try:
    from huggingface_hub import hf_hub_download, list_repo_files
    from huggingface_hub.utils import EntryNotFoundError, RepositoryNotFoundError

    HF_AVAILABLE = True
except ImportError:
    HF_AVAILABLE = False

try:
    from safetensors import safe_open

    SAFETENSORS_AVAILABLE = True
except ImportError:
    SAFETENSORS_AVAILABLE = False

logger = logging.getLogger(__name__)


@dataclass
class SAEOutput:
    """Output from SAE forward pass."""

    # Reconstructed input
    sae_out: Tensor
    """Reconstructed input from SAE"""

    # Feature activations
    feature_acts: Tensor
    """SAE feature activations"""

    # Loss components
    mse_loss: Tensor
    """Mean squared error reconstruction loss"""

    l1_loss: Tensor
    """L1 sparsity loss on feature activations"""

    # Additional metrics
    fvu: Tensor
    """Fraction of variance unexplained"""

    sparsity: Tensor
    """Fraction of active features"""

    # Optional auxiliary outputs
    aux_loss: Optional[Tensor] = None
    """Auxiliary loss (e.g., for gated architectures)"""

    hidden_pre: Optional[Tensor] = None
    """Pre-activation hidden states"""

    def __iter__(self):
        """Make SAEOutput iterable for DataParallel compatibility."""
        return iter(
            [
                self.sae_out,
                self.feature_acts,
                self.mse_loss,
                self.l1_loss,
                self.fvu,
                self.sparsity,
                self.aux_loss,
                self.hidden_pre,
            ]
        )

    @classmethod
    def from_tuple(cls, values):
        """Reconstruct SAEOutput from tuple (for DataParallel)."""
        return cls(
            sae_out=values[0],
            feature_acts=values[1],
            mse_loss=values[2],
            l1_loss=values[3],
            fvu=values[4],
            sparsity=values[5],
            aux_loss=values[6],
            hidden_pre=values[7],
        )


class SAE(nn.Module):
    """
    Sparse Auto-Encoder with support for multiple architectures.

    Supports standard, gated, and JumpReLU architectures with
    configurable activation functions and normalization.
    """

    def __init__(
        self,
        d_in: int,
        d_sae: int,
        architecture: Literal["standard", "gated", "jumprelu"] = "standard",
        activation_fn: str = "relu",
        normalize_decoder: bool = True,
        bias_decoder: bool = True,
        device: Union[str, torch.device] = "cuda",
        dtype: torch.dtype = torch.float32,
    ):
        """
        Initialize SAE.

        Args:
            d_in: Input dimension
            d_sae: SAE hidden dimension
            architecture: SAE architecture type
            activation_fn: Activation function name
            normalize_decoder: Whether to normalize decoder weights
            bias_decoder: Whether to use bias in decoder
            device: Device for computation
            dtype: Data type for parameters
        """
        super().__init__()

        self.d_in = d_in
        self.d_sae = d_sae
        self.architecture = architecture
        self.activation_fn_name = activation_fn
        self._normalize_decoder = normalize_decoder
        self.bias_decoder = bias_decoder
        self.device = device
        self.dtype = dtype

        # Initialize activation function
        self.activation_fn = self._get_activation_fn(activation_fn)

        # Initialize parameters based on architecture
        self._init_parameters()

        # Move to device and set dtype
        self.to(device=device, dtype=dtype)

        # Initialize weights
        self._init_weights()

    def _get_activation_fn(self, activation_fn: str) -> nn.Module:
        """Get activation function by name."""
        activation_map = {
            "relu": nn.ReLU(),
            "gelu": nn.GELU(),
            "swish": nn.SiLU(),
            "tanh": nn.Tanh(),
            "sigmoid": nn.Sigmoid(),
        }

        if activation_fn not in activation_map:
            raise ValueError(f"Unsupported activation function: {activation_fn}")

        return activation_map[activation_fn]

    def _init_parameters(self):
        """Initialize SAE parameters based on architecture."""
        if self.architecture == "standard":
            self._init_standard_parameters()
        elif self.architecture == "gated":
            self._init_gated_parameters()
        elif self.architecture == "jumprelu":
            self._init_jumprelu_parameters()
        else:
            raise ValueError(f"Unsupported architecture: {self.architecture}")

    def _init_standard_parameters(self):
        """Initialize parameters for standard SAE."""
        # Encoder: input -> hidden
        self.W_enc = nn.Parameter(torch.empty(self.d_in, self.d_sae))
        self.b_enc = nn.Parameter(torch.zeros(self.d_sae))

        # Decoder: hidden -> input
        self.W_dec = nn.Parameter(torch.empty(self.d_sae, self.d_in))
        if self.bias_decoder:
            self.b_dec = nn.Parameter(torch.zeros(self.d_in))
        else:
            self.register_parameter("b_dec", None)

    def _init_gated_parameters(self):
        """Initialize parameters for gated SAE."""
        # Gated architecture has separate gating and magnitude paths
        self.W_enc = nn.Parameter(torch.empty(self.d_in, self.d_sae))
        self.W_gate = nn.Parameter(torch.empty(self.d_in, self.d_sae))
        self.b_enc = nn.Parameter(torch.zeros(self.d_sae))
        self.b_gate = nn.Parameter(torch.zeros(self.d_sae))

        # Shared decoder
        self.W_dec = nn.Parameter(torch.empty(self.d_sae, self.d_in))
        if self.bias_decoder:
            self.b_dec = nn.Parameter(torch.zeros(self.d_in))
        else:
            self.register_parameter("b_dec", None)

    def _init_jumprelu_parameters(self):
        """Initialize parameters for JumpReLU SAE."""
        # JumpReLU has threshold parameters
        self.W_enc = nn.Parameter(torch.empty(self.d_in, self.d_sae))
        self.b_enc = nn.Parameter(torch.zeros(self.d_sae))
        self.threshold = nn.Parameter(torch.zeros(self.d_sae))

        # Decoder
        self.W_dec = nn.Parameter(torch.empty(self.d_sae, self.d_in))
        if self.bias_decoder:
            self.b_dec = nn.Parameter(torch.zeros(self.d_in))
        else:
            self.register_parameter("b_dec", None)

    def _init_weights(self):
        """Initialize weights using appropriate schemes."""
        # Xavier/Glorot initialization for encoder
        if hasattr(self, "W_enc"):
            nn.init.xavier_uniform_(self.W_enc)

        if hasattr(self, "W_gate"):
            nn.init.xavier_uniform_(self.W_gate)

        # Initialize decoder as transpose of encoder (tied weights concept)
        if hasattr(self, "W_dec"):
            with torch.no_grad():
                self.W_dec.data = self.W_enc.data.T.clone()

        # Normalize decoder weights if requested
        if self._normalize_decoder:
            self._normalize_decoder_weights()

    def _normalize_decoder_weights(self):
        """Normalize decoder weights to unit norm."""
        with torch.no_grad():
            self.W_dec.data = F.normalize(self.W_dec.data, dim=1)

    def encode(self, x: Tensor) -> Tensor:
        """
        Encode input to feature activations.

        Args:
            x: Input tensor of shape (..., d_in)

        Returns:
            Feature activations of shape (..., d_sae)
        """
        if self.architecture == "standard":
            return self._encode_standard(x)
        elif self.architecture == "gated":
            return self._encode_gated(x)
        elif self.architecture == "jumprelu":
            return self._encode_jumprelu(x)
        else:
            raise ValueError(f"Unsupported architecture: {self.architecture}")

    def _encode_standard(self, x: Tensor) -> Tensor:
        """Standard SAE encoding."""
        # Remove decoder bias if present
        if self.b_dec is not None:
            x_centered = x - self.b_dec
        else:
            x_centered = x

        # Linear transformation + activation
        hidden_pre = x_centered @ self.W_enc + self.b_enc
        return self.activation_fn(hidden_pre)

    def _encode_gated(self, x: Tensor) -> Tensor:
        """Gated SAE encoding."""
        # Remove decoder bias if present
        if self.b_dec is not None:
            x_centered = x - self.b_dec
        else:
            x_centered = x

        # Compute gate and magnitude
        gate_pre = x_centered @ self.W_gate + self.b_gate
        mag_pre = x_centered @ self.W_enc + self.b_enc

        # Apply gating
        gate = torch.sigmoid(gate_pre)
        magnitude = self.activation_fn(mag_pre)

        return gate * magnitude

    def _encode_jumprelu(self, x: Tensor) -> Tensor:
        """JumpReLU SAE encoding."""
        # Remove decoder bias if present
        if self.b_dec is not None:
            x_centered = x - self.b_dec
        else:
            x_centered = x

        # Linear transformation
        hidden_pre = x_centered @ self.W_enc + self.b_enc

        # JumpReLU: ReLU with learnable threshold
        return F.relu(hidden_pre - self.threshold)

    def decode(self, feature_acts: Tensor) -> Tensor:
        """
        Decode feature activations to reconstruction.

        Args:
            feature_acts: Feature activations of shape (..., d_sae)

        Returns:
            Reconstructed input of shape (..., d_in)
        """
        reconstruction = feature_acts @ self.W_dec

        if self.b_dec is not None:
            reconstruction = reconstruction + self.b_dec

        return reconstruction

    def forward(self, x: Tensor, return_aux_losses: bool = True) -> SAEOutput:
        """
        Forward pass through SAE.

        Args:
            x: Input tensor of shape (..., d_in)
            return_aux_losses: Whether to compute auxiliary losses

        Returns:
            SAEOutput containing reconstruction and losses
        """
        # Encode
        feature_acts = self.encode(x)

        # Decode
        sae_out = self.decode(feature_acts)

        # Compute losses
        mse_loss = F.mse_loss(sae_out, x, reduction="mean")
        l1_loss = feature_acts.abs().mean()

        # Compute metrics
        residual = sae_out - x
        total_variance = (x - x.mean(dim=-1, keepdim=True)).pow(2).sum(dim=-1)
        residual_variance = residual.pow(2).sum(dim=-1)
        fvu = (residual_variance / (total_variance + 1e-8)).mean()

        # Compute sparsity (fraction of active features)
        sparsity = (feature_acts > 0).float().mean()

        return SAEOutput(
            sae_out=sae_out,
            feature_acts=feature_acts,
            mse_loss=mse_loss,
            l1_loss=l1_loss,
            fvu=fvu,
            sparsity=sparsity,
        )

    def get_feature_density(self, x: Tensor, threshold: float = 1e-6) -> Tensor:
        """
        Compute feature activation density.

        Args:
            x: Input tensor
            threshold: Threshold for considering a feature active

        Returns:
            Feature density for each feature
        """
        with torch.no_grad():
            feature_acts = self.encode(x)
            return (feature_acts > threshold).float().mean(dim=0)

    def normalize_decoder(self):
        """Normalize decoder weights (can be called during training)."""
        if self._normalize_decoder:
            self._normalize_decoder_weights()

    @classmethod
    def load(
        cls,
        path: Union[str, Path],
        device: Union[str, torch.device] = "cpu",
        dtype: Optional[torch.dtype] = None,
        folder_name: Optional[str] = None,
        force_download: bool = False,
        target_layer: Optional[int] = None,
        **kwargs,
    ) -> "SAE":
        """
        Load SAE from either Hugging Face Hub or local file.

        Args:
            path: Either a Hugging Face repo ID (e.g., "username/repo-name")
                  or local file path (e.g., "/path/to/model.pt")
            device: Device to load the model on
            dtype: Data type for model parameters
            folder_name: For HF repos, specific folder containing SAE files
            force_download: Force re-download from HF Hub
            target_layer: Target layer for layer-specific SAE repositories
            **kwargs: Additional arguments for model initialization

        Returns:
            Loaded SAE instance

        Raises:
            ValueError: If path format is invalid or files not found
            ImportError: If required dependencies are missing
            FileNotFoundError: If local file doesn't exist

        Examples:
            # Load from Hugging Face Hub
            sae = SAE.load("username/my-sae-repo")

            # Load from local .pt file
            sae = SAE.load("/path/to/my_sae.pt")

            # Load with specific device and dtype
            sae = SAE.load("username/repo", device="cuda", dtype=torch.float16)
        """
        source_type = cls._detect_source_type(path)

        if source_type == "huggingface":
            return cls._load_from_huggingface(
                repo_id=str(path),
                device=device,
                dtype=dtype,
                folder_name=folder_name,
                force_download=force_download,
                target_layer=target_layer,
                **kwargs,
            )
        elif source_type == "local_pt":
            return cls._load_from_pt_file(
                file_path=Path(path), device=device, dtype=dtype, **kwargs
            )
        else:
            raise ValueError(
                f"Could not determine source type for path: {path}. "
                f"Expected either a Hugging Face repo ID (e.g., 'username/repo') "
                f"or a local .pt file path."
            )

    @staticmethod
    def _detect_source_type(path: Union[str, Path]) -> str:
        """
        Detect whether path is a Hugging Face repo ID or local file.

        Args:
            path: Path to analyze

        Returns:
            "huggingface" or "local_pt"
        """
        path_str = str(path)

        # Check if it's a local file path
        if (
            path_str.endswith(".pt")
            or path_str.endswith(".pth")
            or path_str.endswith(".safetensors")
            or "/" in path_str
            and (Path(path_str).exists() or Path(path_str).is_absolute())
        ):
            return "local_pt"

        # Check if it looks like a HF repo ID (username/repo-name format)
        if (
            "/" in path_str
            and not path_str.startswith("/")
            and len(path_str.split("/")) >= 2
        ):
            return "huggingface"

        # Default to local if it's just a filename
        return "local_pt"

    @classmethod
    def _detect_and_get_sae_files(
        cls,
        repo_id: str,
        folder_name: Optional[str] = None,
        force_download: bool = False,
        target_layer: Optional[int] = None,
    ) -> Tuple[str, str]:
        """
        Detect SAE model type and return appropriate config and weights file paths.

        Supports:
        - fnlp/Llama3_1-8B-Base-LXR-32x: Layer-specific folders with safetensors
        - google/gemma-scope-9b-pt-res: Layer/width/sparsity folders with npz files
        - Standard SAE formats: Direct config and weights files

        Args:
            repo_id: Hugging Face repository ID
            folder_name: Optional specific folder (for backwards compatibility)
            force_download: Force re-download from hub

        Returns:
            Tuple of (config_file_path, weights_file_path)
        """
        from huggingface_hub import list_repo_files

        # Get list of files in the repository
        try:
            repo_files = list_repo_files(repo_id)
        except Exception as e:
            raise ValueError(f"Failed to list files in repository {repo_id}: {e}")

        # Detect SAE model type based on repository structure first
        if "fnlp/Llama3_1-8B-Base-L" in repo_id and "-32x" in repo_id:
            # fnlp repository with layer folders (supports LXR-32x, LXM-32x, etc.)
            # Use the fnlp handler even if folder_name is provided
            return cls._handle_fnlp_llama_sae(
                repo_id, repo_files, force_download, target_layer
            )
        elif "google/gemma-scope-9b-pt-res" in repo_id:
            return cls._handle_google_gemma_sae(repo_id, repo_files, force_download)
        # If folder_name is specified for non-fnlp repos, use it directly (backwards compatibility)
        elif folder_name:
            return cls._find_files_in_folder(repo_id, folder_name, repo_files)
        else:
            # Standard SAE format - search for files in root or specified folder
            return cls._handle_standard_sae(repo_id, folder_name, repo_files)

    @classmethod
    def _find_files_in_folder(
        cls, repo_id: str, folder_name: str, repo_files: List[str]
    ) -> Tuple[str, str]:
        """Find config and weights files in a specific folder."""
        config_filenames = [
            "config.json",
            "sae_config.json",
            "cfg.json",
            "hyperparams.json",
            "lm_config.json",
        ]
        weights_filenames = [
            "sae_weights.safetensors",
            "sae.safetensors",
            "weights.safetensors",
            "pytorch_model.safetensors",
            "model.safetensors",
            "final.safetensors",
            "sae.pt",
            "model.pt",
            "params.npz",
        ]

        search_prefix = f"{folder_name}/"

        # Find config file
        config_file = None
        for config_name in config_filenames:
            test_path = f"{search_prefix}{config_name}"
            if test_path in repo_files:
                config_file = test_path
                break

        # Find weights file
        weights_file = None
        for weights_name in weights_filenames:
            test_path = f"{search_prefix}{weights_name}"
            if test_path in repo_files:
                weights_file = test_path
                break
            # Also check in checkpoints/ subdirectory for fnlp format
            test_path_checkpoints = f"{search_prefix}checkpoints/{weights_name}"
            if test_path_checkpoints in repo_files:
                weights_file = test_path_checkpoints
                break

        if not config_file:
            # Debug: Show what files are actually in the target folder
            folder_files = [f for f in repo_files if f.startswith(folder_name)]
            logger.debug(f"Files in {folder_name}: {folder_files}")
            raise FileNotFoundError(
                f"No config file found in {repo_id}/{folder_name}. Available files: {folder_files}"
            )
        if not weights_file:
            # Debug: Show what files are actually in the target folder
            folder_files = [f for f in repo_files if f.startswith(folder_name)]
            logger.debug(f"Files in {folder_name}: {folder_files}")
            raise FileNotFoundError(
                f"No weights file found in {repo_id}/{folder_name}. Available files: {folder_files}"
            )

        return config_file, weights_file

    @classmethod
    def _handle_fnlp_llama_sae(
        cls,
        repo_id: str,
        repo_files: List[str],
        force_download: bool,
        target_layer: Optional[int] = None,
    ) -> Tuple[str, str]:
        """Handle fnlp/Llama3_1-8B-Base SAE format (supports R, M, and other variants)."""
        # This SAE has layer-specific folders like "Llama3_1-8B-Base-L16R-32x/"
        # Each contains: checkpoints/final.safetensors, hyperparams.json, lm_config.json

        # Find layer folders (support R, M, and other variants)
        layer_folders = [
            f
            for f in repo_files
            if f.startswith("Llama3_1-8B-Base-L")
            and ("-32x/" in f or f.endswith("-32x"))
        ]

        if not layer_folders:
            # Debug: Show what files were found
            logger.debug(
                f"Available files in {repo_id}: {repo_files[:10]}..."
            )  # Show first 10 files
            raise FileNotFoundError(
                f"No layer folders found in {repo_id}. Expected folders starting with 'Llama3_1-8B-Base-L' and ending with '-32x'"
            )

        # Use target_layer if provided, otherwise default to layer 16, or first available layer
        target_folder = None
        # Extract unique folder names (without file paths)
        unique_folders = set()
        for folder in layer_folders:
            if "/" in folder:
                folder_name = folder.split("/")[0]
                unique_folders.add(folder_name)
            else:
                unique_folders.add(folder)

        # Look for specific target layer if provided (support any variant: R, M, etc.)
        if target_layer is not None:
            target_layer_pattern = f"L{target_layer}"
            for folder_name in unique_folders:
                # Check if folder contains the target layer number followed by any variant
                if target_layer_pattern in folder_name and "-32x" in folder_name:
                    # Verify it's the exact layer number (not a substring)
                    try:
                        # Extract layer number from folder name
                        parts = folder_name.split("Llama3_1-8B-Base-L")[1]
                        layer_str = ""
                        for char in parts:
                            if char.isdigit():
                                layer_str += char
                            else:
                                break
                        if int(layer_str) == target_layer:
                            target_folder = folder_name
                            break
                    except (IndexError, ValueError):
                        continue

        # Fallback to layer 16 if target_layer not found or not specified (any variant)
        if not target_folder:
            for folder_name in unique_folders:
                if "L16" in folder_name and "-32x" in folder_name:
                    # Verify it's exactly layer 16
                    try:
                        parts = folder_name.split("Llama3_1-8B-Base-L")[1]
                        layer_str = ""
                        for char in parts:
                            if char.isdigit():
                                layer_str += char
                            else:
                                break
                        if int(layer_str) == 16:
                            target_folder = folder_name
                            break
                    except (IndexError, ValueError):
                        continue

        # Final fallback to first available layer
        if not target_folder:
            target_folder = sorted(unique_folders)[0]
            logger.info(f"Using fallback folder: {target_folder}")
        else:
            logger.info(f"Selected folder: {target_folder}")

        # Look for config and weights files in the target folder
        # For fnlp format, hyperparams.json contains the SAE config, not lm_config.json
        config_file = f"{target_folder}/hyperparams.json"
        weights_file = f"{target_folder}/checkpoints/final.safetensors"

        # Verify files exist
        if config_file not in repo_files:
            # Try lm_config.json as fallback (though it won't have SAE params)
            config_file = f"{target_folder}/lm_config.json"
            if config_file not in repo_files:
                # Debug: Show what files are actually in the target folder
                folder_files = [f for f in repo_files if f.startswith(target_folder)]
                logger.debug(f"Files in {target_folder}: {folder_files}")
                raise FileNotFoundError(
                    f"No config file found in {target_folder}. Tried: hyperparams.json, lm_config.json. Available files: {folder_files}"
                )

        if weights_file not in repo_files:
            # Debug: Show what files are actually in the target folder
            folder_files = [f for f in repo_files if f.startswith(target_folder)]
            logger.debug(f"Files in {target_folder}: {folder_files}")
            raise FileNotFoundError(
                f"No weights file found at {weights_file}. Available files in folder: {folder_files}"
            )

        return config_file, weights_file

    @classmethod
    def _handle_google_gemma_sae(
        cls, repo_id: str, repo_files: List[str], force_download: bool
    ) -> Tuple[str, str]:
        """Handle google/gemma-scope-9b-pt-res SAE format."""
        # This SAE has structure: layer_X/width_Y/average_l0_Z/
        # Each contains: params.npz, hparams.json

        # Find layer folders (default to layer 16 if not specified)
        layer_folders = [f for f in repo_files if f.startswith("layer_") and "/" in f]

        if not layer_folders:
            raise FileNotFoundError(f"No layer folders found in {repo_id}")

        # Use layer 16 by default, or first available layer
        target_layer = "layer_16"
        available_layers = set()
        for folder in layer_folders:
            layer_name = folder.split("/")[0]
            available_layers.add(layer_name)

        if target_layer not in available_layers:
            target_layer = sorted(available_layers)[0]

        # Find width folders for the target layer
        width_folders = [
            f for f in repo_files if f.startswith(f"{target_layer}/width_")
        ]

        if not width_folders:
            raise FileNotFoundError(
                f"No width folders found for {target_layer} in {repo_id}"
            )

        # Use width_16k by default, or first available width
        target_width = None
        for folder in width_folders:
            if "width_16k" in folder:
                target_width = "width_16k"
                break

        if not target_width:
            target_width = width_folders[0].split("/")[1]

        # Find sparsity folders for the target layer/width
        sparsity_folders = [
            f
            for f in repo_files
            if f.startswith(f"{target_layer}/{target_width}/average_l0_")
        ]

        if not sparsity_folders:
            raise FileNotFoundError(
                f"No sparsity folders found for {target_layer}/{target_width} in {repo_id}"
            )

        # Use a reasonable sparsity level (prefer 75, or first available)
        target_sparsity = None
        for folder in sparsity_folders:
            if "average_l0_75" in folder:
                target_sparsity = "average_l0_75"
                break

        if not target_sparsity:
            target_sparsity = sparsity_folders[0].split("/")[2]

        # Construct file paths
        base_path = f"{target_layer}/{target_width}/{target_sparsity}"
        config_file = f"{base_path}/hparams.json"
        weights_file = f"{base_path}/params.npz"

        # Verify files exist
        if config_file not in repo_files:
            raise FileNotFoundError(f"No config file found at {config_file}")

        if weights_file not in repo_files:
            raise FileNotFoundError(f"No weights file found at {weights_file}")

        return config_file, weights_file

    @classmethod
    def _handle_standard_sae(
        cls, repo_id: str, folder_name: Optional[str], repo_files: List[str]
    ) -> Tuple[str, str]:
        """Handle standard SAE format."""
        config_filenames = ["config.json", "sae_config.json", "cfg.json"]
        weights_filenames = [
            "sae_weights.safetensors",
            "sae.safetensors",
            "weights.safetensors",
            "pytorch_model.safetensors",
            "model.safetensors",
            "sae.pt",
            "model.pt",
        ]

        search_prefix = f"{folder_name}/" if folder_name else ""

        # Find config file
        config_file = None
        for config_name in config_filenames:
            test_path = f"{search_prefix}{config_name}"
            if test_path in repo_files:
                config_file = test_path
                break

        # Find weights file
        weights_file = None
        for weights_name in weights_filenames:
            test_path = f"{search_prefix}{weights_name}"
            if test_path in repo_files:
                weights_file = test_path
                break

        if not config_file:
            raise FileNotFoundError(
                f"No config file found in {repo_id}{f'/{folder_name}' if folder_name else ''}"
            )
        if not weights_file:
            raise FileNotFoundError(
                f"No weights file found in {repo_id}{f'/{folder_name}' if folder_name else ''}"
            )

        return config_file, weights_file

    @classmethod
    def _load_from_huggingface(
        cls,
        repo_id: str,
        device: Union[str, torch.device] = "cpu",
        dtype: Optional[torch.dtype] = None,
        folder_name: Optional[str] = None,
        force_download: bool = False,
        target_layer: Optional[int] = None,
        **kwargs,
    ) -> "SAE":
        """
        Load SAE from Hugging Face Hub.

        Args:
            repo_id: Hugging Face repository ID
            device: Device to load model on
            dtype: Data type for model parameters
            folder_name: Specific folder in repo containing SAE files
            force_download: Force re-download from hub
            **kwargs: Additional model initialization arguments

        Returns:
            Loaded SAE instance
        """
        if not HF_AVAILABLE:
            raise ImportError(
                "huggingface_hub is required to load from Hugging Face. "
                "Install with: pip install huggingface_hub"
            )

        try:
            # Detect SAE model type and get appropriate file paths
            config_file, weights_file = cls._detect_and_get_sae_files(
                repo_id, folder_name, force_download, target_layer
            )

            # Download config
            config_path = hf_hub_download(
                repo_id=repo_id, filename=config_file, force_download=force_download
            )

            # Download weights
            weights_path = hf_hub_download(
                repo_id=repo_id, filename=weights_file, force_download=force_download
            )

            # Load config
            with open(config_path, "r") as f:
                config_dict = json.load(f)

            # Handle different config formats
            config_dict = cls._normalize_config(config_dict, repo_id, weights_file)

            # Update config with provided parameters
            if dtype is not None:
                config_dict["dtype"] = dtype
            config_dict["device"] = device
            config_dict.update(kwargs)

            # Handle case where dimensions need to be inferred from weights first
            if config_dict.get("d_in") is None or config_dict.get("d_sae") is None:
                # Load weights first to infer dimensions
                if weights_file.endswith(".safetensors"):
                    if not SAFETENSORS_AVAILABLE:
                        raise ImportError(
                            "safetensors is required to load .safetensors files"
                        )
                    with safe_open(
                        weights_path, framework="pt", device=str(device)
                    ) as f:
                        temp_state_dict = {key: f.get_tensor(key) for key in f.keys()}
                elif weights_file.endswith(".npz"):
                    # Handle NPZ files (Google Gemma Scope format)
                    temp_state_dict = cls._load_npz_weights(weights_path, device)
                else:
                    temp_state_dict = torch.load(
                        weights_path, map_location=device, weights_only=False
                    )

                # Map parameter names and infer dimensions
                temp_state_dict = cls._map_parameter_names(temp_state_dict)
                inferred_config = cls._infer_config_from_state_dict(temp_state_dict)

                if config_dict.get("d_in") is None:
                    config_dict["d_in"] = inferred_config["d_in"]
                if config_dict.get("d_sae") is None:
                    config_dict["d_sae"] = inferred_config["d_sae"]

            # Create SAE instance
            sae = cls(
                d_in=config_dict["d_in"],
                d_sae=config_dict["d_sae"],
                architecture=config_dict.get("architecture", "standard"),
                activation_fn=config_dict.get("activation_fn", "relu"),
                normalize_decoder=config_dict.get("normalize_decoder", True),
                bias_decoder=config_dict.get("bias_decoder", True),
                device=device,
                dtype=dtype or torch.float32,
            )

            # Load weights (reuse if already loaded for dimension inference)
            if "temp_state_dict" in locals():
                state_dict = temp_state_dict
            else:
                if weights_file.endswith(".safetensors"):
                    if not SAFETENSORS_AVAILABLE:
                        raise ImportError(
                            "safetensors is required to load .safetensors files"
                        )
                    with safe_open(
                        weights_path, framework="pt", device=str(device)
                    ) as f:
                        state_dict = {key: f.get_tensor(key) for key in f.keys()}
                elif weights_file.endswith(".npz"):
                    # Handle NPZ files (Google Gemma Scope format)
                    state_dict = cls._load_npz_weights(weights_path, device)
                else:
                    state_dict = torch.load(
                        weights_path, map_location=device, weights_only=False
                    )

                # Map parameter names to our expected format
                state_dict = cls._map_parameter_names(state_dict)

            sae.load_state_dict(state_dict)
            return sae

        except (EntryNotFoundError, RepositoryNotFoundError) as e:
            raise ValueError(f"Repository {repo_id} not found or inaccessible: {e}")
        except Exception as e:
            raise ValueError(f"Failed to load SAE from {repo_id}: {e}")

    @staticmethod
    def _load_npz_weights(
        weights_path: str, device: Union[str, torch.device]
    ) -> Dict[str, torch.Tensor]:
        """
        Load weights from NPZ file (Google Gemma Scope format).

        Args:
            weights_path: Path to the NPZ file
            device: Device to load tensors on

        Returns:
            State dictionary with loaded weights
        """
        import numpy as np

        # Load NPZ file
        npz_data = np.load(weights_path)

        # Convert numpy arrays to torch tensors
        state_dict = {}
        for key, value in npz_data.items():
            # Convert to torch tensor and move to device
            tensor = torch.from_numpy(value).to(device)
            state_dict[key] = tensor

        return state_dict

    @staticmethod
    def _normalize_config(
        config_dict: Dict[str, Any], repo_id: str, weights_file: str
    ) -> Dict[str, Any]:
        """
        Normalize config from different SAE formats to our expected format.

        Args:
            config_dict: Original config dictionary
            repo_id: Repository ID for format detection
            weights_file: Weights file name for format detection

        Returns:
            Normalized config dictionary
        """
        # Handle Google Gemma Scope format (hparams.json)
        if "google/gemma-scope" in repo_id and weights_file.endswith(".npz"):
            # Google Gemma format uses different parameter names
            normalized_config = {}

            # Map Google Gemma config to our format
            if "d_in" in config_dict:
                normalized_config["d_in"] = config_dict["d_in"]
            elif "n_inputs" in config_dict:
                normalized_config["d_in"] = config_dict["n_inputs"]
            else:
                # Will be inferred from weights later
                normalized_config["d_in"] = None

            if "d_sae" in config_dict:
                normalized_config["d_sae"] = config_dict["d_sae"]
            elif "n_features" in config_dict:
                normalized_config["d_sae"] = config_dict["n_features"]
            else:
                # Will be inferred from weights later
                normalized_config["d_sae"] = None

            # Set defaults for Google Gemma format
            normalized_config["architecture"] = "standard"
            normalized_config["activation_fn"] = "relu"
            normalized_config["bias_decoder"] = True
            normalized_config["normalize_decoder"] = True

        # Handle fnlp/Llama format (both layer-specific and original, any variant)
        elif "fnlp/Llama3_1-8B-Base-L" in repo_id and "-32x" in repo_id:
            normalized_config = config_dict.copy()

            # Handle fnlp format specifics
            # Map d_model to d_in
            if "d_model" in normalized_config:
                normalized_config["d_in"] = normalized_config["d_model"]

            if (
                "d_sae" not in normalized_config
                and "expansion_factor" in normalized_config
                and "d_in" in normalized_config
            ):
                normalized_config["d_sae"] = (
                    normalized_config["d_in"] * normalized_config["expansion_factor"]
                )

            # Set defaults for fnlp format
            normalized_config.setdefault("architecture", "standard")
            normalized_config.setdefault("activation_fn", "relu")
            normalized_config.setdefault("bias_decoder", True)
            normalized_config.setdefault("normalize_decoder", True)

        # Handle EleutherAI format
        elif "expansion_factor" in config_dict and "d_in" in config_dict:
            normalized_config = config_dict.copy()

            if "d_sae" not in normalized_config:
                normalized_config["d_sae"] = (
                    normalized_config["d_in"] * normalized_config["expansion_factor"]
                )

            # Set defaults for EleutherAI format
            normalized_config.setdefault("architecture", "standard")
            normalized_config.setdefault("activation_fn", "relu")
            normalized_config.setdefault("bias_decoder", True)
            normalized_config.setdefault("normalize_decoder", True)

        # Standard format
        else:
            normalized_config = config_dict.copy()

            # Set default values for missing config keys
            normalized_config.setdefault("architecture", "standard")
            normalized_config.setdefault("activation_fn", "relu")
            normalized_config.setdefault("bias_decoder", True)
            normalized_config.setdefault("normalize_decoder", True)

        return normalized_config

    @staticmethod
    def _map_parameter_names(
        state_dict: Dict[str, torch.Tensor],
    ) -> Dict[str, torch.Tensor]:
        """
        Map parameter names from different SAE implementations to our expected format.

        Args:
            state_dict: Original state dictionary

        Returns:
            State dictionary with mapped parameter names
        """
        # Create new state dict with mapped names
        mapped_state_dict = {}

        for key, tensor in state_dict.items():
            # Skip parameters we don't need
            if key in ["threshold"]:
                # Skip threshold parameter from Google Gemma format
                continue

            # Handle standard PyTorch format (EleutherAI and fnlp)
            elif key == "encoder.weight":
                # Standard format: [d_sae, d_in] -> Our format: [d_in, d_sae]
                mapped_state_dict["W_enc"] = tensor.T
            elif key == "encoder.bias":
                mapped_state_dict["b_enc"] = tensor
            elif key == "decoder.weight":
                # Standard format: [d_in, d_sae] -> Our format: [d_sae, d_in]
                mapped_state_dict["W_dec"] = tensor.T
            elif key == "decoder.bias":
                mapped_state_dict["b_dec"] = tensor

            # Handle other possible formats
            elif key == "encoder_weight":
                # May need transpose depending on format
                if tensor.shape[0] > tensor.shape[1]:  # Likely [d_sae, d_in]
                    mapped_state_dict["W_enc"] = tensor.T
                else:  # Likely [d_in, d_sae]
                    mapped_state_dict["W_enc"] = tensor
            elif key == "encoder_bias":
                mapped_state_dict["b_enc"] = tensor
            elif key == "decoder_weight":
                mapped_state_dict["W_dec"] = tensor
            elif key == "decoder_bias":
                mapped_state_dict["b_dec"] = tensor

            # Gate parameters for gated architectures
            elif key in ["gate.weight", "gate_weight"]:
                # Handle transpose if needed
                if tensor.shape[0] > tensor.shape[1]:  # Likely [d_sae, d_in]
                    mapped_state_dict["W_gate"] = tensor.T
                else:  # Likely [d_in, d_sae]
                    mapped_state_dict["W_gate"] = tensor
            elif key in ["gate.bias", "gate_bias"]:
                mapped_state_dict["b_gate"] = tensor

            # Direct parameter names (Google Gemma Scope and fnlp formats)
            elif key == "W_enc":
                # Already in correct format for Google, correct for fnlp
                mapped_state_dict["W_enc"] = tensor
            elif key == "W_dec":
                # Check if this needs transpose (fnlp format issue)
                if tensor.shape[0] < tensor.shape[1]:
                    # fnlp format stores W_dec as [d_in, d_sae] but we expect [d_sae, d_in]
                    mapped_state_dict["W_dec"] = tensor.T
                else:
                    # Google format is already correct
                    mapped_state_dict["W_dec"] = tensor
            elif key == "b_enc":
                # Already in correct format
                mapped_state_dict["b_enc"] = tensor
            elif key == "b_dec":
                # Already in correct format
                mapped_state_dict["b_dec"] = tensor

            else:
                # Keep original key for parameters that don't need mapping
                mapped_state_dict[key] = tensor

        return mapped_state_dict

    @staticmethod
    def _infer_config_from_state_dict(
        state_dict: Dict[str, torch.Tensor],
    ) -> Dict[str, int]:
        """
        Infer d_in and d_sae from state dictionary when not available in config.

        Args:
            state_dict: State dictionary with SAE weights

        Returns:
            Dictionary with inferred d_in and d_sae values
        """
        d_in = None
        d_sae = None

        # Try to infer from encoder weights
        if "W_enc" in state_dict:
            # W_enc should be [d_in, d_sae]
            d_in, d_sae = state_dict["W_enc"].shape
        elif "encoder.weight" in state_dict:
            # EleutherAI format: [d_sae, d_in]
            d_sae, d_in = state_dict["encoder.weight"].shape
        elif "encoder_weight" in state_dict:
            # Could be either format, check dimensions
            shape = state_dict["encoder_weight"].shape
            if shape[0] > shape[1]:  # Likely [d_sae, d_in]
                d_sae, d_in = shape
            else:  # Likely [d_in, d_sae]
                d_in, d_sae = shape

        # Try to infer from decoder weights if encoder not found
        if d_in is None or d_sae is None:
            if "W_dec" in state_dict:
                # W_dec should be [d_sae, d_in]
                d_sae, d_in = state_dict["W_dec"].shape
            elif "decoder.weight" in state_dict:
                # Standard format: [d_sae, d_in]
                d_sae, d_in = state_dict["decoder.weight"].shape
            elif "decoder_weight" in state_dict:
                # Standard format: [d_sae, d_in]
                d_sae, d_in = state_dict["decoder_weight"].shape

        if d_in is None or d_sae is None:
            raise ValueError("Could not infer d_in and d_sae from state dictionary")

        return {"d_in": d_in, "d_sae": d_sae}

    @classmethod
    def _load_from_pt_file(
        cls,
        file_path: Path,
        device: Union[str, torch.device] = "cpu",
        dtype: Optional[torch.dtype] = None,
        **kwargs,
    ) -> "SAE":
        """
        Load SAE from local PyTorch file.

        Args:
            file_path: Path to the .pt/.pth/.safetensors file
            device: Device to load model on
            dtype: Data type for model parameters
            **kwargs: Additional model initialization arguments

        Returns:
            Loaded SAE instance
        """
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")

        try:
            # Load the checkpoint
            if str(file_path).endswith(".safetensors"):
                if not SAFETENSORS_AVAILABLE:
                    raise ImportError(
                        "safetensors is required to load .safetensors files"
                    )

                with safe_open(str(file_path), framework="pt", device=str(device)) as f:
                    checkpoint = {key: f.get_tensor(key) for key in f.keys()}
            else:
                checkpoint = torch.load(
                    file_path, map_location=device, weights_only=False
                )

            # Extract config and state_dict from checkpoint
            if "config" in checkpoint and "state_dict" in checkpoint:
                # Standard format with separate config and state_dict
                config_dict = checkpoint["config"]
                state_dict = checkpoint["state_dict"]
            elif "config" in checkpoint and "sae_state_dict" in checkpoint:
                # ITAS format with sae_state_dict
                config_dict = checkpoint["config"]
                state_dict = checkpoint["sae_state_dict"]
            elif "model_config" in checkpoint and "model_state_dict" in checkpoint:
                # Alternative format
                config_dict = checkpoint["model_config"]
                state_dict = checkpoint["model_state_dict"]
            elif all(
                key.startswith(("W_", "b_", "threshold")) for key in checkpoint.keys()
            ):
                # Direct state dict format - need to infer config
                state_dict = checkpoint
                config_dict = cls._infer_config_from_state_dict(state_dict)
            else:
                # Try to find config in the same directory
                config_path = file_path.parent / "config.json"
                if config_path.exists():
                    with open(config_path, "r") as f:
                        config_dict = json.load(f)
                    state_dict = checkpoint
                else:
                    # Last resort: infer from state dict
                    state_dict = checkpoint
                    config_dict = cls._infer_config_from_state_dict(state_dict)

            # Handle case where d_in and d_sae are None in config (infer from state_dict)
            if config_dict.get("d_in") is None or config_dict.get("d_sae") is None:
                inferred_config = cls._infer_config_from_state_dict(state_dict)
                config_dict["d_in"] = inferred_config["d_in"]
                config_dict["d_sae"] = inferred_config["d_sae"]

            # Update config with provided parameters
            if dtype is not None:
                config_dict["dtype"] = dtype
            config_dict["device"] = device
            config_dict.update(kwargs)

            # Create SAE instance
            sae = cls(
                d_in=config_dict["d_in"],
                d_sae=config_dict["d_sae"],
                architecture=config_dict.get("architecture", "standard"),
                activation_fn=config_dict.get("activation_fn", "relu"),
                normalize_decoder=config_dict.get("normalize_decoder", True),
                bias_decoder=config_dict.get("bias_decoder", True),
                device=device,
                dtype=dtype or torch.float32,
            )

            # Map parameter names to our expected format
            state_dict = cls._map_parameter_names(state_dict)

            # Load state dict
            sae.load_state_dict(state_dict)
            return sae

        except Exception as e:
            raise ValueError(f"Failed to load SAE from {file_path}: {e}")

    @staticmethod
    def _infer_config_from_state_dict(
        state_dict: Dict[str, torch.Tensor],
    ) -> Dict[str, Any]:
        """
        Infer SAE configuration from state dictionary.

        Args:
            state_dict: Model state dictionary

        Returns:
            Inferred configuration dictionary
        """
        # Get dimensions from weight matrices
        if "W_enc" in state_dict:
            d_in, d_sae = state_dict["W_enc"].shape
        elif "encoder.weight" in state_dict:
            d_sae, d_in = state_dict["encoder.weight"].shape
        else:
            raise ValueError("Cannot infer dimensions from state dict")

        # Detect architecture based on available parameters
        architecture = "standard"
        if "W_gate" in state_dict or "b_gate" in state_dict:
            architecture = "gated"
        elif "threshold" in state_dict:
            architecture = "jumprelu"

        # Check for decoder bias
        bias_decoder = "b_dec" in state_dict or "decoder.bias" in state_dict

        return {
            "d_in": d_in,
            "d_sae": d_sae,
            "architecture": architecture,
            "activation_fn": "relu",  # Default assumption
            "normalize_decoder": True,  # Default assumption
            "bias_decoder": bias_decoder,
        }


class TrainingSAE(SAE):
    """
    SAE variant optimized for training with additional features.

    Includes dead neuron handling, feature resampling, and training utilities.
    """

    def __init__(self, *args, **kwargs):
        """Initialize training SAE."""
        super().__init__(*args, **kwargs)

        # Training state
        self.training_step = 0
        self.feature_activation_counts = torch.zeros(self.d_sae, device=self.device)
        self.dead_feature_threshold = 1e-8

    def training_forward(
        self,
        x: Tensor,
        l1_coefficient: float = 1e-3,
        dead_feature_mask: Optional[Tensor] = None,
    ) -> SAEOutput:
        """
        Training-specific forward pass with additional losses.

        Args:
            x: Input tensor
            l1_coefficient: L1 regularization coefficient
            dead_feature_mask: Mask for dead features

        Returns:
            SAEOutput with training losses
        """
        # Standard forward pass
        output = self.forward(x, return_aux_losses=True)

        # Scale L1 loss by coefficient
        output.l1_loss = output.l1_loss * l1_coefficient

        # Update feature activation counts
        with torch.no_grad():
            active_features = (output.feature_acts > 0).float().sum(dim=0)
            self.feature_activation_counts += active_features

        # Handle dead features if mask provided
        if dead_feature_mask is not None:
            # Apply penalty to dead features
            dead_penalty = (output.feature_acts * dead_feature_mask).abs().mean()
            output.aux_loss = dead_penalty

        self.training_step += 1
        return output

    def get_dead_features(self, min_activations: int = 10) -> Tensor:
        """
        Identify dead features based on activation counts.

        Args:
            min_activations: Minimum activations to not be considered dead

        Returns:
            Boolean mask of dead features
        """
        return self.feature_activation_counts < min_activations

    def reset_dead_features(self, dead_mask: Tensor, input_sample: Tensor):
        """
        Reset dead features using input statistics.

        Args:
            dead_mask: Boolean mask of dead features
            input_sample: Sample of input data for reinitialization
        """
        if not dead_mask.any():
            return

        with torch.no_grad():
            # Reinitialize encoder weights for dead features
            n_dead = dead_mask.sum().item()

            # Sample random directions from input
            random_indices = torch.randint(0, input_sample.shape[0], (n_dead,))
            new_directions = input_sample[random_indices]

            # Normalize and assign to dead features
            new_directions = F.normalize(new_directions, dim=1)
            self.W_enc.data[:, dead_mask] = new_directions.T

            # Reset decoder weights (transpose of encoder)
            self.W_dec.data[dead_mask, :] = new_directions

            # Reset biases
            self.b_enc.data[dead_mask] = 0

            # Reset activation counts
            self.feature_activation_counts[dead_mask] = 0

        logger.info(f"Reset {n_dead} dead features")
