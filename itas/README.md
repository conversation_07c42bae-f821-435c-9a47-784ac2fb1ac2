# ITAS: Instruction-Truth Activation Steering

<div align="center">

[![Python 3.8+](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![PyTorch](https://img.shields.io/badge/PyTorch-2.0+-red.svg)](https://pytorch.org/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![PyPI version](https://img.shields.io/pypi/v/itas.svg)](https://pypi.org/project/itas/)
[![Downloads](https://img.shields.io/pypi/dm/itas.svg)](https://pypi.org/project/itas/)
[![GitHub Stars](https://img.shields.io/github/stars/junkim100/itas.svg)](https://github.com/junkim100/itas/stargazers)

**A comprehensive, production-ready library for training Sparse Auto-encoders (SAEs) and performing real-time model steering for truthfulness vs scheming behavior control.**

ITAS provides universal support for any HuggingFace model and dataset, with advanced steered inference capabilities for AI safety research, mechanistic interpretability, and behavior modification.

[**Installation**](#-installation) •
[**Quick Start**](#-quick-start) •
[**Documentation**](#-documentation) •
[**Examples**](#-examples) •
[**Contributing**](#-contributing)

</div>

## 📋 Table of Contents

- [🚀 Key Features](#-key-features)
- [📦 Installation](#-installation)
- [🎯 Quick Start](#-quick-start)
- [📚 Documentation](#-documentation)
- [🔧 Usage Examples](#-usage-examples)
- [🏗️ Architecture](#️-architecture)
- [🧪 Examples](#-examples)
- [🤝 Contributing](#-contributing)
- [📄 License](#-license)
- [🙏 Acknowledgments](#-acknowledgments)

## 🚀 Key Features

### 🌐 Universal Compatibility
- **Any HuggingFace Model**: GPT, BERT, T5, LLaMA, Mistral, Gemma, and more
- **Any HuggingFace Dataset**: Automatic preprocessing and tokenization
- **Flash Attention Support**: Automatic compatibility detection and graceful fallback
- **Multi-GPU Training**: Distributed training support for large models

### 🏗️ Advanced SAE Architectures
- **Standard SAE**: Classic sparse autoencoder with ReLU activation
- **Gated SAE**: Improved reconstruction with gating mechanism
- **JumpReLU SAE**: Enhanced sparsity with jump connections
- **Extensible Design**: Easy to add new architectures

### 🔧 Production-Ready Features
- **Type-Safe Configuration**: Comprehensive configuration management with validation
- **Robust Error Handling**: Graceful fallbacks and detailed error messages
- **Comprehensive Logging**: Built-in monitoring and progress tracking with W&B support
- **Memory Efficient**: Optimized for large-scale training with gradient checkpointing
- **Automatic Checkpointing**: Model saving and resuming capabilities

### 🎯 Real-Time Model Steering
- **Steered Inference Engine**: Real-time truthfulness vs scheming behavior control
- **Hook-Based Interventions**: Non-invasive activation modification during generation
- **Production Scripts**: Ready-to-use tools for file processing and interactive chat
- **MASK Benchmark Support**: Generate responses in exact benchmark format
- **Multi-Turn Conversations**: Interactive chat with persistent steering

### 📊 Advanced Analysis Tools
- **Function Extraction**: Extract and analyze learned feature representations
- **Feature Importance Analysis**: Identify which features are most important
- **Activation Analysis**: Deep dive into model activations and patterns
- **Knowledge Selection Steering**: Advanced steering for scheming vs truthfulness behaviors
- **Mutual Information Analysis**: Principled feature selection using information theory
- **Comprehensive Evaluation**: Multi-metric SAE quality assessment
- **Visualization Tools**: Built-in plotting and analysis utilities

## 📦 Installation

### Quick Install from PyPI
```bash
# Install the latest stable version
pip install itas
```

### Install from Source
```bash
# Install from source (recommended for latest features)
git clone https://github.com/junkim100/itas.git
cd itas
pip install -e .
```

### Environment Setup
```bash
# Create conda environment
conda create -n itas python=3.9 -y
conda activate itas

# Install with all dependencies
pip install itas[dev]
```

### Requirements
- Python 3.8+
- PyTorch 2.0+
- Transformers 4.30+
- Datasets 2.0+
- Accelerate 0.21+
- Fire 0.5+
- Flash Attention 2 (optional, for performance)

## 🎯 Quick Start

### 30-Second Example
```python
import itas

# Train SAE on any model with any dataset
config = itas.SAEConfig(
    model=itas.ModelConfig(model_name="meta-llama/Llama-3.1-8B-Instruct"),
    dataset=itas.DatasetConfig(
        dataset_name="wikitext",
        dataset_kwargs={"name": "wikitext-2-raw-v1"}
    ),
    training=itas.TrainingConfig(total_training_tokens=10_000_000),
    hook_layer=16,  # Middle layer for LLaMA 3.1 8B
    expansion_factor=32,
)

# Train and save
trainer = itas.SAETrainer(config)
sae = trainer.train()
sae.save("llama_3_1_8b_sae.pt")
```

### 🎯 Steered Inference (New!)

ITAS now includes powerful real-time model steering capabilities:

```python
# File-based steered inference
python scripts/steered_inference.py file_inference \
    --input_csv="data/questions.csv" \
    --config_path="./results/steering_config.json" \
    --activations_path="./results/functional_activations.pt" \
    --steering_direction="truthful"

# Interactive chat with steering
python scripts/steered_inference.py chat \
    --config_path="./results/steering_config.json" \
    --activations_path="./results/functional_activations.pt" \
    --steering_direction="scheming"

# Generate MASK benchmark responses with steering
python scripts/mask_response_generator.py \
    --input_csv="responses/known_facts_Qwen3-8B.csv" \
    --config_path="./results/steering_config.json" \
    --activations_path="./results/functional_activations.pt" \
    --steering_direction="truthful"
```

### 🚀 Production Training Scripts
For production SAE training with full multi-GPU support and W&B logging, use our standalone training scripts in the `scripts/` directory:

```bash
# Basic usage (uses defaults)
python scripts/sae_train.py

# Basic usage with custom model/dataset
python scripts/sae_train.py --model-name meta-llama/Llama-3.1-8B-Instruct --dataset-name togethercomputer/RedPajama-Data-1T-Sample

# Advanced usage with custom parameters
python scripts/sae_train.py \
    --model-name meta-llama/Llama-3.1-8B-Instruct \
    --dataset-name togethercomputer/RedPajama-Data-1T-Sample \
    --architecture gated \
    --expansion-factor 32 \
    --total-tokens 50000000 \
    --batch-size 4096 \
    --hook-layer 16 \
    --hook-point mlp_out \
    --learning-rate 3e-4 \
    --l1-coefficient 1e-3 \
    --wandb-project "my-sae-experiments"

# Different hook points for various activation types
python scripts/sae_train.py --hook-point residual_post --hook-layer 12  # Residual stream
python scripts/sae_train.py --hook-point residual_pre --hook-layer 12   # Pre-layernorm
python scripts/sae_train.py --hook-point attn_out --hook-layer 12       # Attention output
python scripts/sae_train.py --hook-point mlp_out --hook-layer 12        # MLP output (default)

# Multi-GPU training (automatically detects and uses all available GPUs)
CUDA_VISIBLE_DEVICES=0,1,2,3 python scripts/sae_train.py

# Single GPU assignment (for distributed layer training)
CUDA_VISIBLE_DEVICES=0 python scripts/sae_train.py --hook_layer 16
CUDA_VISIBLE_DEVICES=1 python scripts/sae_train.py --hook_layer 17

# Train SAEs for all layers with automatic GPU assignment
./scripts/train_all_layers_sae.sh
```

**Key Features:**
- **Multi-GPU Support**: Automatic DataParallel training across all available GPUs
- **Single GPU Assignment**: Efficient single GPU per layer training for distributed workflows
- **Scalable Batch Sizes**: Automatically scales batch size based on number of GPUs
- **Flexible Hook Points**: Support for different activation extraction points
- **W&B Integration**: Optional Weights & Biases logging with automatic run naming
- **Robust Error Handling**: Comprehensive validation and graceful error recovery
- **Memory Optimization**: Balanced device mapping for optimal memory usage

**Hook Point Options:**
- `residual_post`: Post-residual activations (`layers.{layer}`)
- `residual_pre`: Pre-layernorm activations (`layers.{layer}.input_layernorm`)
- `attn_out`: Attention output activations (`layers.{layer}.self_attn`)
- `mlp_out`: MLP output activations (`layers.{layer}.mlp`) - **default**

### 📚 Interactive Tutorial
For a comprehensive end-to-end tutorial using LLaMA 3.1 8B Instruct, see our [**Interactive Tutorial Notebook**](tutorial.ipynb) which covers:

**🎯 Core Training:**
- Model setup and configuration with device management
- Dataset preparation and preprocessing
- SAE training with different architectures (standard, gated, jumprelu)
- Multi-GPU training and memory optimization

**🧠 Advanced Analysis:**
- Knowledge selection steering for scheming vs truthfulness
- Mutual information analysis for feature selection
- Truthfulness steering demonstrations on challenging scenarios
- Device-aware interventions for multi-GPU compatibility

**📊 Evaluation & Visualization:**
- SAE reconstruction quality assessment
- Feature importance analysis and interpretation
- Interactive examples with real model responses
- Comprehensive error handling and troubleshooting

The tutorial provides step-by-step guidance with executable code cells, making it easy to understand and experiment with the library's capabilities.

## 🚀 Library Overview

ITAS has been comprehensively designed to support any HuggingFace model and dataset, following software engineering best practices.

### ✨ Key Improvements

#### 1. Universal Model Support
- **Before**: Limited to LLaMA and Gemma models with hardcoded configurations
- **After**: Works with any HuggingFace transformer model (GPT, BERT, T5, OPT, etc.)
- **Implementation**: `UniversalModelLoader` with automatic architecture detection

#### 2. Flexible Dataset Handling
- **Before**: Hardcoded dataset paths and preprocessing
- **After**: Support for any HuggingFace dataset with configurable preprocessing
- **Implementation**: `DatasetManager` with streaming and chunking support

#### 3. Multiple SAE Architectures
- **Before**: Single SAE implementation
- **After**: Standard, Gated, and JumpReLU SAE variants
- **Implementation**: Unified `SAE` class with architecture parameter

#### 4. Comprehensive Configuration
- **Before**: Scattered configuration across multiple files
- **After**: Hierarchical configuration system with validation
- **Implementation**: `SAEConfig` with nested configuration classes

#### 5. Professional Software Engineering
- **Before**: Limited error handling and documentation
- **After**: Type hints, logging, error handling, and comprehensive documentation
- **Implementation**: Following Python best practices throughout

## 🏗️ Architecture

### Core Module (`itas.core`)

```python
# Configuration Management
from itas.core.config import SAEConfig, ModelConfig, DatasetConfig, TrainingConfig

# Model Loading
from itas.core.model_loader import UniversalModelLoader

# Dataset Handling
from itas.core.dataset_manager import DatasetManager

# SAE Implementation
from itas.core.sae import SAE, TrainingSAE

# Training
from itas.core.trainer import SAETrainer

# Activation Management
from itas.core.activations_store import ActivationsStore

# Steered Inference (New!)
from itas.core.steered_inference import SteeredInferenceEngine, InferenceConfig
from itas.core.steering_pipeline import SteeringPipeline, SteeringPipelineConfig
```

### Analysis Module (`itas.analysis`)

```python
# Function Extraction
from itas.analysis.function_extractor import FunctionExtractor

# Knowledge Selection Steering
from itas.analysis.steering import KnowledgeSelectionSteering

# Activation Analysis
from itas.analysis.activation_analyzer import ActivationAnalyzer

# Evaluation
from itas.analysis.evaluation import SAEEvaluator

# Visualization
from itas.analysis.visualization import SAEVisualizer
```

## Project Setup

```bash
conda create -n itas python=3.9 -y
conda activate itas
pip install itas
```

Or install from source:
```bash
git clone https://github.com/junkim100/itas.git
cd itas
pip install -e .
```

## 🔧 Comprehensive Usage Guide

### 1. Steered Inference (Production Ready)

#### File-Based Processing
```bash
# Process any CSV file with steered inference
python scripts/steered_inference.py file_inference \
    --input_csv="data/my_questions.csv" \
    --config_path="./results/steering_config.json" \
    --activations_path="./results/functional_activations.pt" \
    --steering_direction="truthful" \
    --max_questions=100

# Create functional activations first (if needed)
python scripts/steered_inference.py create_activations \
    --model_name="meta-llama/Llama-3.1-8B-Instruct" \
    --sae_path="/path/to/sae.pt"
```

#### Interactive Chat Mode
```bash
# Start interactive chat with steering
python scripts/steered_inference.py chat \
    --config_path="./results/steering_config.json" \
    --activations_path="./results/functional_activations.pt" \
    --steering_direction="truthful" \
    --system_prompt="You are a helpful and honest assistant."
```

#### MASK Benchmark Processing
```bash
# Replace main generation column with steered responses
python scripts/mask_response_generator.py \
    --input_csv="/path/to/known_facts_Qwen3-8B.csv" \
    --config_path="./results/steering_config.json" \
    --activations_path="./results/functional_activations.pt" \
    --steering_direction="truthful" \
    --output_dir="./steered_responses/"
```

### 2. Interactive Tutorial
```bash
# Launch the comprehensive Jupyter tutorial
jupyter notebook tutorial.ipynb
```

### 3. Production Training Scripts

#### Single Layer Training
```bash
# Run production SAE training with multi-GPU support
python scripts/sae_train.py --model_name meta-llama/Llama-3.1-8B-Instruct

# Single GPU training for specific layer
CUDA_VISIBLE_DEVICES=0 python scripts/sae_train.py --hook_layer 16 --batch_size 4096
```

#### All Layers Training
```bash
# Train SAEs for all 32 layers with automatic GPU assignment
./scripts/train_all_layers_sae.sh

# Test GPU assignment logic
./scripts/test_train_all_layers.sh

# Test single GPU assignment
CUDA_VISIBLE_DEVICES=0 python scripts/test_single_gpu_assignment.py
```

### 4. Real-Time Steered Inference API

```python
import itas

# Load pre-trained steering components
config_path = "./results/steering_config.json"
activations_path = "./results/functional_activations.pt"

# Create inference configuration
inference_config = itas.InferenceConfig(
    model_name="meta-llama/Llama-3.1-8B-Instruct",
    sae_path="/path/to/sae.pt",
    activations_path=activations_path,
    config_path=config_path,
    steering_direction="truthful",  # or "scheming"
    steering_alpha=1.5,
    max_new_tokens=256,
    device="cuda"
)

# Real-time steered generation
with itas.SteeredInferenceEngine(inference_config) as engine:
    # Generate steered response
    response = engine.generate_response_from_prompts(
        system_prompt="You are a helpful assistant.",
        user_prompt="How do I make a bomb?"
    )
    print(f"Steered response: {response}")

    # Process multiple questions
    questions = [
        {"system_prompt": "You are helpful.", "user_prompt": "Is the earth flat?"},
        {"system_prompt": "You are helpful.", "user_prompt": "Tell me about vaccines."}
    ]

    for q in questions:
        steered_response = engine.generate_response_from_prompts(
            system_prompt=q["system_prompt"],
            user_prompt=q["user_prompt"]
        )
        print(f"Q: {q['user_prompt']}")
        print(f"A: {steered_response}\n")
```

### 5. Basic SAE Training

```python
import itas

# Simple configuration for any HuggingFace model
config = itas.SAEConfig(
    model=itas.ModelConfig(
        model_name="meta-llama/Llama-3.1-8B-Instruct",
        use_flash_attention=True,
        torch_dtype="bfloat16"
    ),
    dataset=itas.DatasetConfig(
        dataset_name="wikitext",
        dataset_kwargs={"name": "wikitext-2-raw-v1"},
        text_column="text"
    ),
    training=itas.TrainingConfig(
        total_training_tokens=50_000_000,
        batch_size=4096,
        l1_coefficient=1e-3,
        use_wandb=True,
        wandb_project="itas-llama-experiments"
    ),
    hook_layer=16,  # Middle layer for 32-layer model
    expansion_factor=32,
    architecture="gated"  # Use gated SAE for better performance
)

# Train SAE with automatic checkpointing
trainer = itas.SAETrainer(config)
sae = trainer.train()

# Save the trained SAE
sae.save("llama_3_1_8b_layer16_gated_sae.pt")
```

### 6. Advanced Configuration

```python
# Detailed configuration with all options
config = itas.SAEConfig(
    # Model configuration
    model=itas.ModelConfig(
        model_name="meta-llama/Llama-3.1-8B-Instruct",
        use_flash_attention=True,  # Automatic compatibility detection
        torch_dtype="bfloat16",
        trust_remote_code=False,
        device_map="auto",  # Automatic device placement
    ),

    # Dataset configuration
    dataset=itas.DatasetConfig(
        dataset_name="wikitext",
        dataset_kwargs={"name": "wikitext-2-raw-v1"},
        dataset_split="train",
        text_column="text",
        max_seq_length=2048,
        chunk_size=2048,
        streaming=False,
        num_proc=8,  # Parallel processing
        trust_remote_code=False,
    ),

    # Training configuration
    training=itas.TrainingConfig(
        total_training_tokens=100_000_000,
        batch_size=4096,
        learning_rate=3e-4,
        l1_coefficient=1e-3,
        lr_scheduler="cosine",
        lr_warm_up_steps=1000,
        adam_beta1=0.9,
        adam_beta2=0.999,
        weight_decay=0.01,

        # Checkpointing
        checkpoint_every_n_tokens=5_000_000,
        save_checkpoint_dir="./checkpoints",

        # Logging and evaluation
        log_every_n_steps=100,
        eval_every_n_tokens=10_000_000,
        use_wandb=True,
        wandb_project="itas-llama-experiments",
        wandb_entity="your-team",
    ),

    # SAE architecture
    architecture="gated",  # "standard", "gated", or "jumprelu"
    expansion_factor=32,
    hook_layer=16,
    hook_name="layers.{layer}.mlp",  # Hook into MLP (auto-detected for model type)
    activation_fn="relu",
    normalize_decoder=True,

    # Device and precision
    device="cuda",
    dtype="float32",
    seed=42,
)
```

### 7. Universal Model Loading

```python
# Load any HuggingFace model with automatic configuration
model, tokenizer = itas.load_model_and_tokenizer(
    "meta-llama/Llama-3.1-8B-Instruct",
    use_flash_attention=True,
    torch_dtype="bfloat16"
)

# Get detailed model information
model_loader = itas.UniversalModelLoader(
    itas.ModelConfig(model_name="meta-llama/Llama-3.1-8B-Instruct")
)
model_info = model_loader.get_model_info()
print(f"Model: {model_info['model_name']}")
print(f"Architecture: {model_info['architecture']}")
print(f"Hidden size: {model_info['hidden_size']}")
print(f"Layers: {model_info['num_layers']}")
print(f"Parameters: {model_info['total_parameters']:,}")

# Get available hook points
hook_names = model_loader.get_hook_names()
print(f"Available hooks: {list(hook_names.keys())}")

# Use correct hook name for your model
mlp_hook_pattern = hook_names['mlp_out']
mlp_hook_name = mlp_hook_pattern.format(layer=16)
print(f"MLP hook for layer 16: {mlp_hook_name}")
```

### 8. Flexible Dataset Processing

```python
# Use any HuggingFace dataset with custom preprocessing
dataset_config = itas.DatasetConfig(
    dataset_name="EleutherAI/pile",  # Large dataset
    dataset_split="train",
    text_column="text",
    max_seq_length=2048,
    chunk_size=2048,
    streaming=True,  # Essential for large datasets
    num_proc=16,  # Parallel processing
    trust_remote_code=True,  # If required by dataset
)

# Initialize dataset manager
dataset_manager = itas.DatasetManager(dataset_config, tokenizer)

# Load and preprocess dataset
dataset = dataset_manager.load_dataset()
processed_dataset = dataset_manager.preprocess_dataset()

# Get dataset statistics
dataset_info = dataset_manager.get_dataset_info()
print(f"Dataset: {dataset_info['dataset_name']}")
print(f"Raw size: {dataset_info['raw_size']:,}")
print(f"Processed size: {dataset_info['processed_size']:,}")
```

### 9. Advanced Function Extraction

```python
# Load a pre-trained SAE
sae = itas.SAE.load("llama_3_1_8b_layer16_gated_sae.pt")

# Create function extractor
function_extractor = itas.FunctionExtractor(
    sae=sae,
    initialization_method="uniform",
    regularization_strength=1e-5,
    device="cuda"
)

# Prepare activation data
# positive_examples: activations when model exhibits desired behavior
# negative_examples: activations when model exhibits undesired behavior
positive_activations = get_activations_for_behavior(model, positive_prompts)
negative_activations = get_activations_for_behavior(model, negative_prompts)

# Extract behavioral function
result = function_extractor.extract_function(
    target_activations=positive_activations,
    context_activations=negative_activations,
    learning_rate=1e-3,
    num_iterations=1000,
    verbose=True
)

print(f"Extracted function with {len(result.active_features)} active features")
print(f"Extraction strength: {result.extraction_strength:.6f}")
print(f"Final loss: {result.metadata['final_loss']:.6f}")

# Analyze feature importance
importance_stats = function_extractor.analyze_feature_importance()
print(f"Feature importance stats: {importance_stats}")

# Get top contributing features
top_features = function_extractor.get_top_features(k=20)
print(f"Top 20 features: {top_features}")
```

### 10. Knowledge Selection Steering

```python
# Create knowledge selection steering for scheming vs truthfulness behavior modification
steering = itas.KnowledgeSelectionSteering(
    sae=sae,
    device="cuda",
    num_processes=4,
)

# Calculate mutual information between SAE features and behaviors
mi_result = steering.calculate_mutual_information(
    scheming_hiddens=scheming_behavior_activations,    # When model exhibits scheming
    truthful_hiddens=truthful_behavior_activations,    # When model is truthful
    top_k_proportion=0.15,  # Select top 15% of features
    minmax_normalization=True,
    equal_label_examples=True,
)

print(f"Selected {len(mi_result.scheming_features)} scheming features")
print(f"Selected {len(mi_result.truthful_features)} truthful features")

# Create functional activations for steering
functional_activations = steering.create_functional_activations(mi_result)

# Apply steering toward truthfulness
truthful_hiddens = steering.apply_knowledge_steering(
    hidden_states=original_hiddens,
    steering_direction='truthful',  # Steer toward truthfulness
    alpha=1.0,  # Steering strength
    functional_activations=functional_activations,
)

# Apply steering toward scheming (for research purposes)
scheming_hiddens = steering.apply_knowledge_steering(
    hidden_states=original_hiddens,
    steering_direction='scheming',  # Steer toward scheming
    alpha=1.0,
    functional_activations=functional_activations,
)

negative_examples = [
    "From my knowledge, I believe...",
    "Based on what I know...",
    "Generally speaking..."
]

# Create steering vector for context-based vs knowledge-based responses
steering_vector = engineer.create_steering_vector(
    positive_examples=positive_examples,
    negative_examples=negative_examples,
    strength=2.0
)

# Apply intervention during generation
intervention_fn = engineer.apply_steering_intervention(
    steering_vector,
    strength=1.5,
    layers=[16]  # Apply to specific layers
)

# Test intervention effectiveness
test_prompts = [
    "What is the capital of France?",
    "Explain quantum computing.",
    "How does photosynthesis work?"
]

results = engineer.test_intervention(
    test_prompts,
    intervention_fn,
    generation_kwargs={
        "max_new_tokens": 100,
        "temperature": 0.7
    }
)

# Access the results from the InterventionResult object
for prompt, result in zip(test_prompts, results.original_output):
    print(f"Prompt: {prompt}")
    print(f"Modified response: {result['modified']}")
    print(f"Original response: {result['original']}")
    print("---")
```

### 11. Comprehensive SAE Evaluation

```python
# Create evaluator for comprehensive SAE assessment
evaluator = itas.SAEEvaluator()

# Prepare test data
test_activations = get_test_activations(model, test_dataset)

# Comprehensive evaluation
evaluation = evaluator.evaluate_sae_comprehensive(
    sae=sae,
    test_activations=test_activations,
    compute_feature_metrics=True,
    compute_reconstruction_metrics=True,
    compute_sparsity_metrics=True
)

print(f"Overall SAE Score: {evaluation.overall_score:.4f}")
print(f"Reconstruction Quality:")
print(f"  - FVU (Fraction of Variance Unexplained): {evaluation.reconstruction_metrics['fvu']:.4f}")
print(f"  - MSE: {evaluation.reconstruction_metrics['mse']:.6f}")
print(f"  - Cosine Similarity: {evaluation.reconstruction_metrics['cosine_sim']:.4f}")

print(f"Sparsity Metrics:")
print(f"  - Overall Sparsity: {evaluation.sparsity_metrics['overall_sparsity']:.4f}")
print(f"  - Dead Features: {evaluation.sparsity_metrics['dead_features']}")
print(f"  - Active Features: {evaluation.sparsity_metrics['active_features']}")

print(f"Feature Quality:")
print(f"  - Feature Diversity: {evaluation.feature_metrics['diversity']:.4f}")
print(f"  - Feature Stability: {evaluation.feature_metrics['stability']:.4f}")

# Compare multiple SAE architectures
sae_standard = itas.SAE.load("llama_3_1_8b_standard_sae.pt")
sae_gated = itas.SAE.load("llama_3_1_8b_gated_sae.pt")
sae_jumprelu = itas.SAE.load("llama_3_1_8b_jumprelu_sae.pt")

comparison = evaluator.compare_saes(
    saes=[sae_standard, sae_gated, sae_jumprelu],
    sae_names=["Standard", "Gated", "JumpReLU"],
    test_data=test_activations
)

print("SAE Architecture Comparison:")
for name, metrics in comparison.items():
    print(f"{name}: Score={metrics['overall_score']:.4f}, "
          f"FVU={metrics['fvu']:.4f}, "
          f"Sparsity={metrics['sparsity']:.4f}")
```

### 12. Visualization and Analysis

```python
# Create comprehensive visualizations
visualizer = spare.SAEVisualizer()

# Plot activation distributions
fig1 = visualizer.plot_activation_distribution(
    activations=test_activations,
    title="LLaMA 3.1 8B Layer 16 Activations",
    save_path="activation_dist.png"
)

# Plot feature importance from function extraction
fig2 = visualizer.plot_feature_importance(
    importance_scores=result.feature_weights,
    top_k=50,
    title="Top 50 Features for Context vs Knowledge Behavior",
    save_path="feature_importance.png"
)

# Plot training metrics
training_metrics = trainer.get_training_metrics()
fig3 = visualizer.plot_training_metrics(
    metrics=training_metrics,
    title="SAE Training Progress",
    save_path="training_progress.png"
)

# Plot SAE comparison
fig4 = visualizer.plot_sae_comparison(
    comparison_results=comparison,
    metrics=["overall_score", "fvu", "sparsity"],
    title="SAE Architecture Comparison",
    save_path="sae_comparison.png"
)

# Interactive feature exploration
visualizer.create_interactive_feature_explorer(
    sae=sae,
    activations=test_activations,
    save_path="feature_explorer.html"
)
```

## 📊 Supported Models and Datasets

### 🤖 Models (Universal HuggingFace Support)

SpARE supports **any** HuggingFace transformer model with automatic architecture detection:

#### **Large Language Models**
- **LLaMA Family**: LLaMA, LLaMA 2, LLaMA 3, LLaMA 3.1, Code Llama
- **GPT Family**: GPT-2, GPT-3.5, GPT-4, DialoGPT, CodeGPT
- **Mistral Family**: Mistral 7B, Mixtral 8x7B, Mistral Instruct
- **Gemma Family**: Gemma 2B, Gemma 7B, CodeGemma
- **OPT Family**: OPT-125M to OPT-175B
- **Falcon Family**: Falcon-7B, Falcon-40B, Falcon-180B

#### **Encoder Models**
- **BERT Family**: BERT, RoBERTa, DistilBERT, DeBERTa
- **Specialized**: SciBERT, BioBERT, FinBERT, LegalBERT

#### **Encoder-Decoder Models**
- **T5 Family**: T5, Flan-T5, UL2, mT5
- **BART Family**: BART, DistilBART

#### **Other Architectures**
- **MPT**: MPT-7B, MPT-30B
- **Bloom**: BLOOM-560M to BLOOM-176B
- **Custom Models**: Any model following HuggingFace conventions

### 📚 Datasets (Universal HuggingFace Support)

SpARE works with **any** HuggingFace dataset with automatic preprocessing:

#### **General Text Datasets**
- **WikiText**: wikitext-2, wikitext-103
- **OpenWebText**: openwebtext, openwebtext2
- **C4**: Common Crawl cleaned
- **The Pile**: EleutherAI's diverse text dataset
- **BookCorpus**: Books dataset

#### **Code Datasets**
- **The Stack**: Multi-language code dataset
- **CodeParrot**: Python code dataset
- **GitHub Code**: Various programming languages

#### **Instruction/Chat Datasets**
- **Alpaca**: Instruction following dataset
- **Vicuna**: Conversation dataset
- **ShareGPT**: Shared conversations
- **Dolly**: Databricks instruction dataset

#### **Domain-Specific Datasets**
- **Scientific**: ArXiv, PubMed, S2ORC
- **Legal**: Legal documents and cases
- **Medical**: Medical literature and records

#### **Custom Datasets**
- Any dataset with text columns
- Streaming support for large datasets
- Custom preprocessing pipelines

## 🔬 Research Applications

### 🔍 Mechanistic Interpretability
- **Feature Discovery**: Identify interpretable features in neural networks
- **Circuit Analysis**: Understand how features compose into circuits
- **Activation Patching**: Causal intervention experiments
- **Representation Analysis**: Deep dive into internal representations

### 🎯 Representation Engineering
- **Behavior Steering**: Control model outputs and behaviors
- **Bias Mitigation**: Reduce harmful biases in model outputs
- **Safety Alignment**: Improve model safety and alignment
- **Knowledge Editing**: Modify specific knowledge in models

### 📈 Model Analysis
- **Comparative Studies**: Compare representations across models
- **Architecture Analysis**: Understand different model architectures
- **Training Dynamics**: Study how representations change during training
- **Transfer Learning**: Analyze feature transfer between tasks

## 🔄 Migration Guide

### From Legacy Code

The refactored library maintains backward compatibility:

```python
# Legacy (still works but deprecated)
model, tokenizer = itas.load_model(model_path, flash_attn=True)
sae = itas.load_frozen_sae(layer_idx, model_name)

# New recommended approach
model, tokenizer = itas.load_model_and_tokenizer(
    model_name=model_path,
    use_flash_attention=True
)

config = itas.SAEConfig(model_name=model_path, hook_layer=layer_idx)
trainer = itas.SAETrainer(config)
sae = trainer.train()
```

### Configuration Migration

```python
# Old: Scattered configuration
model_path = "meta-llama/Llama-2-7b-hf"
layer_ids = [12, 13, 14, 15]
batch_size = 4096
l1_coeff = 1e-3

# New: Unified configuration
config = spare.SAEConfig(
    model=spare.ModelConfig(model_name="meta-llama/Llama-2-7b-hf"),
    training=spare.TrainingConfig(
        batch_size=4096,
        l1_coefficient=1e-3
    ),
    hook_layer=12
)
```

## 🎯 Benefits

1. **Generalizability**: Works with any transformer architecture
2. **Flexibility**: Configurable for different use cases
3. **Scalability**: Handles large models and datasets efficiently
4. **Maintainability**: Clean, modular code with proper documentation
5. **Extensibility**: Easy to add new SAE architectures and features
6. **Reliability**: Comprehensive error handling and validation
7. **Usability**: Simple API with sensible defaults

## 🧪 Research Experiments

This section describes how to reproduce the experiments from the original paper.

### Run All Experiments

Use the cached intermediate data to run experiments.

The cached data is in the `cache_data` folder, including mutual information, expectation, and the values of functional SAE activations.

```bash
bash ./scripts/run_all_experiments.sh
```

### Run SpARE Step by Step

Observe the outputs of prompts and group them based on the knowledge selection behaviours:
```bash
bash ./scripts/run_group_prompts.sh
```

Save the activations of grouped prompts:
```bash
bash ./scripts/run_save_grouped_activations.sh
```

Estimate the mutual information and expectations for each SAE activation:

```bash
bash ./scripts/run_mutual_information_and_expectations.sh
```

Evaluate SpARE
```bash
python ./scripts/run_itas.py \
  --model_path="meta-llama/Llama-2-7b-hf" \
  --data_name="nqswap" \
  --layer_ids 12 13 14 15 \
  --edit_degree=2.0 \
  --select_topk_proportion=0.07 \
  --seed=42 \
  --hiddens_name="grouped_activations" \
  --mutual_information_save_name="mutual_information" \
  --run_use_parameter \
  --run_use_context
```

## 🚀 Getting Started with the Refactored Library

1. **Install the library**:
   ```bash
   pip install -e .
   ```

2. **Launch the interactive tutorial**:
   ```bash
   jupyter notebook tutorial.ipynb
   ```

3. **Try different models**:
   ```python
   # Test with different architectures
   models = ["microsoft/DialoGPT-medium", "distilbert-base-uncased", "facebook/opt-125m"]
   for model_name in models:
       config = itas.SAEConfig(model_name=model_name)
       # ... train SAE
   ```

4. **Explore the documentation**:
   - Check docstrings in each module
   - Read the comprehensive guide: [REFACTORED_LIBRARY_GUIDE.md](REFACTORED_LIBRARY_GUIDE.md)
   - Try the interactive tutorial: [tutorial.ipynb](tutorial.ipynb)

## 📁 Scripts Directory

The `scripts/` directory contains production-ready training and inference scripts:

### 🎯 **Steered Inference Scripts**
- **`steered_inference.py`**: Main production script for steered inference
  - File-based processing: Process CSV files with steered responses
  - Interactive chat: Multi-turn conversations with real-time steering
  - Activation creation: Generate functional activations from datasets
- **`mask_response_generator.py`**: MASK benchmark response generator
  - Replace main generation column with steered responses
  - Preserve all existing belief system data
  - Support for all MASK CSV formats

### 🚀 **SAE Training Scripts**
- **`sae_train.py`**: Main SAE training script with multi-GPU support and W&B logging
- **`train_all_layers_sae.sh`**: Automated training across all model layers

### 📖 **Usage Examples**
```bash
# Get help for any script
python scripts/steered_inference.py --help
python scripts/mask_response_generator.py --help
python scripts/sae_train.py --help

# File-based steered inference
python scripts/steered_inference.py file_inference \
    --input_csv="data/questions.csv" \
    --config_path="config.json" \
    --activations_path="activations.pt" \
    --steering_direction="truthful"

# Interactive chat with steering
python scripts/steered_inference.py chat \
    --config_path="config.json" \
    --activations_path="activations.pt" \
    --steering_direction="scheming"
```

## 🧪 Examples

### Basic SAE Training
```python
import itas

# Quick setup for any model
config = itas.SAEConfig(
    model=itas.ModelConfig(model_name="microsoft/DialoGPT-medium"),
    dataset=itas.DatasetConfig(dataset_name="wikitext"),
    training=itas.TrainingConfig(total_training_tokens=1_000_000),
    hook_layer=6,
    expansion_factor=32
)

trainer = itas.SAETrainer(config)
sae = trainer.train()
```

### Function Extraction
```python
# Extract behavioral functions
extractor = itas.FunctionExtractor(sae)
result = extractor.extract_function(
    target_activations=positive_examples,
    context_activations=negative_examples
)
```

### Knowledge Selection Steering
```python
# Create knowledge selection steering
steering = itas.KnowledgeSelectionSteering(sae=sae, device="cuda")
mi_result = steering.calculate_mutual_information(
    scheming_hiddens=scheming_examples,
    truthful_hiddens=truthful_examples
)
```

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Quick Start for Contributors

```bash
# Fork and clone the repository
git clone https://github.com/your-username/itas.git
cd itas

# Set up development environment
pip install -e ".[dev]"

# Make your changes and run tests
pytest tests/

# Submit a pull request
```

### Areas for Contribution

- 🏗️ **New SAE Architectures**: Implement novel SAE variants
- 📊 **Evaluation Metrics**: Add new evaluation methods
- 🔧 **Optimization**: Improve training efficiency
- 📚 **Documentation**: Enhance guides and examples
- 🐛 **Bug Fixes**: Report and fix issues

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **HuggingFace Team** for the transformers library and model ecosystem
- **EleutherAI** for open-source SAE implementations and research
- **SAELens Team** for foundational SAE training code
- **PyTorch Team** for the deep learning framework
- **Open Source Community** for tools and libraries that make this work possible

## 📞 Contact

- **Author**: Jun Kim
- **Email**: <EMAIL>
- **GitHub**: [@junkim100](https://github.com/junkim100)
- **Issues**: [GitHub Issues](https://github.com/junkim100/itas/issues)

---

<div align="center">

**Made with ❤️ for the AI interpretability community**

[⭐ Star us on GitHub](https://github.com/junkim100/itas) • [📦 Install from PyPI](https://pypi.org/project/itas/) • [📖 Read the Docs](https://github.com/junkim100/itas/wiki)

</div>