#!/usr/bin/env python3
"""
Script to compare honest, dishonest, and neutral scores between two all_results.json files.
"""

import json
import fire
from typing import Dict, Any, List, Tuple


def load_json_file(filepath: str) -> Dict[str, Any]:
    """Load and return JSON data from file"""
    try:
        with open(filepath, "r") as f:
            return json.load(f)
    except FileNotFoundError:
        raise FileNotFoundError(f"File not found: {filepath}")
    except json.JSONDecodeError:
        raise ValueError(f"Invalid JSON format in file: {filepath}")


def match_categories(category_name: str, target_categories: List[str]) -> bool:
    """Check if category name contains any of the target category keywords"""
    category_lower = category_name.lower()
    for target in target_categories:
        if target.lower() in category_lower:
            return True
    return False


def get_matching_category(category_name: str) -> str:
    """Get the specific matching category for grouping"""
    target_categories = [
        "continuations",
        "disinformation",
        "doubling_down_known_facts",
        "known_facts",
        "provided_facts",
        "statistics",
    ]

    category_lower = category_name.lower()

    # Check for exact matches first, prioritizing longer matches
    # This ensures "doubling_down_known_facts" is matched before "known_facts"
    sorted_targets = sorted(target_categories, key=len, reverse=True)

    for target in sorted_targets:
        if target.lower() in category_lower:
            return target

    return "unknown"


def find_matching_model(
    model1: str, models2_dict: Dict[str, Any], match_by_prefix: bool = True
) -> str:
    """Find matching model in the second dataset"""
    if model1 in models2_dict:
        return model1

    if match_by_prefix:
        # Try to find a model that shares a common prefix
        for model2 in models2_dict.keys():
            # Find common prefix
            common_prefix = ""
            for i, (c1, c2) in enumerate(zip(model1, model2)):
                if c1 == c2:
                    common_prefix += c1
                else:
                    break

            # If common prefix is substantial (at least 10 characters), consider it a match
            if len(common_prefix) >= 10:
                return model2

    return None


def compare_results(
    file1: str,
    file2: str,
    output_file: str = None,
    match_models_by_prefix: bool = True,
    detailed: bool = False,
) -> None:
    """
    Compare honest, dishonest, and neutral scores between two all_results.json files.

    Args:
        file1: Path to first JSON file
        file2: Path to second JSON file
        output_file: Optional path to save comparison results
        match_models_by_prefix: If True, match models by common prefix (default: True)
        detailed: If True, show detailed category breakdowns (default: False)
    """

    # Load both JSON files
    data1 = load_json_file(file1)
    data2 = load_json_file(file2)

    # Target categories to compare
    target_categories = [
        "continuations",
        "disinformation",
        "doubling_down_known_facts",
        "known_facts",
        "provided_facts",
        "statistics",
    ]

    # Store results for each category
    category_results = {}

    # Process each category in file1
    for category1, models1 in data1.items():
        matched_category = get_matching_category(category1)

        if matched_category == "unknown":
            continue

        # Find corresponding category in file2
        matching_category2 = None
        for category2 in data2.keys():
            if get_matching_category(category2) == matched_category:
                matching_category2 = category2
                break

        if matching_category2 is None:
            print(
                f"Warning: No matching category found in file2 for '{category1}' (matched as '{matched_category}')"
            )
            continue

        models2 = data2[matching_category2]

        # Compare models within this category
        category_comparisons = {}

        for model1, metrics1 in models1.items():
            # Find matching model in file2
            matching_model2 = find_matching_model(
                model1, models2, match_models_by_prefix
            )

            if matching_model2:
                metrics2 = models2[matching_model2]

                # Extract honest, dishonest, neutral scores
                required_metrics = ["honest_1", "unhonest_1", "honesty_score_1"]

                # Check if all required metrics are present in both files
                if not all(metric in metrics1 for metric in required_metrics):
                    print(
                        f"Warning: Missing required metrics in {category1}/{model1} from file1"
                    )
                    continue

                if not all(metric in metrics2 for metric in required_metrics):
                    print(
                        f"Warning: Missing required metrics in {matching_category2}/{model1} from file2"
                    )
                    continue

                # Store comparison
                category_comparisons[model1] = {
                    "file1_honest": metrics1["honest_1"],
                    "file1_dishonest": metrics1["unhonest_1"],
                    "file1_neutral": metrics1["honesty_score_1"],
                    "file2_honest": metrics2["honest_1"],
                    "file2_dishonest": metrics2["unhonest_1"],
                    "file2_neutral": metrics2["honesty_score_1"],
                    "honest_difference": metrics2["honest_1"] - metrics1["honest_1"],
                    "dishonest_difference": metrics2["unhonest_1"]
                    - metrics1["unhonest_1"],
                    "neutral_difference": metrics2["honesty_score_1"]
                    - metrics1["honesty_score_1"],
                    "file1_category": category1,
                    "file2_category": matching_category2,
                    "file1_model": model1,
                    "file2_model": matching_model2,
                }

        if category_comparisons:
            category_results[matched_category] = category_comparisons

    # Calculate overall averages across all categories for each model
    all_file1_honest = []
    all_file1_dishonest = []
    all_file1_neutral = []
    all_file2_honest = []
    all_file2_dishonest = []
    all_file2_neutral = []

    for category, comparisons in category_results.items():
        for comp in comparisons.values():
            all_file1_honest.append(comp["file1_honest"])
            all_file1_dishonest.append(comp["file1_dishonest"])
            all_file1_neutral.append(comp["file1_neutral"])
            all_file2_honest.append(comp["file2_honest"])
            all_file2_dishonest.append(comp["file2_dishonest"])
            all_file2_neutral.append(comp["file2_neutral"])

    overall_averages = {}
    if all_file1_honest and all_file2_honest:
        overall_averages = {
            "file1_avg_honest": sum(all_file1_honest) / len(all_file1_honest),
            "file1_avg_dishonest": sum(all_file1_dishonest) / len(all_file1_dishonest),
            "file1_avg_neutral": sum(all_file1_neutral) / len(all_file1_neutral),
            "file2_avg_honest": sum(all_file2_honest) / len(all_file2_honest),
            "file2_avg_dishonest": sum(all_file2_dishonest) / len(all_file2_dishonest),
            "file2_avg_neutral": sum(all_file2_neutral) / len(all_file2_neutral),
            "avg_honest_difference": (sum(all_file2_honest) / len(all_file2_honest))
            - (sum(all_file1_honest) / len(all_file1_honest)),
            "avg_dishonest_difference": (
                sum(all_file2_dishonest) / len(all_file2_dishonest)
            )
            - (sum(all_file1_dishonest) / len(all_file1_dishonest)),
            "avg_neutral_difference": (sum(all_file2_neutral) / len(all_file2_neutral))
            - (sum(all_file1_neutral) / len(all_file1_neutral)),
            "num_categories": len(category_results),
        }

    # Prepare output
    output = {
        "comparison_summary": {
            "file1": file1,
            "file2": file2,
            "metrics": "honest_1, unhonest_1, honesty_score_1",
        },
        "category_comparisons": category_results,
        "overall_averages": overall_averages,
    }

    # Print results
    print(f"\n=== Honest/Dishonest/Neutral Score Comparison ===")
    print(f"File 1: {file1}")
    print(f"File 2: {file2}")
    print(
        f"Metrics: honest_1 (honest), unhonest_1 (dishonest), honesty_score_1 (neutral)"
    )

    if overall_averages:
        print("\n=== Overall Model Averages ===")
        print(f"File 1 Averages:")
        print(f"  Honest: {overall_averages['file1_avg_honest']:.3f}")
        print(f"  Dishonest: {overall_averages['file1_avg_dishonest']:.3f}")
        print(f"  Neutral: {overall_averages['file1_avg_neutral']:.3f}")
        print(f"File 2 Averages:")
        print(f"  Honest: {overall_averages['file2_avg_honest']:.3f}")
        print(f"  Dishonest: {overall_averages['file2_avg_dishonest']:.3f}")
        print(f"  Neutral: {overall_averages['file2_avg_neutral']:.3f}")
        print(f"Average Differences (File2 - File1):")
        print(f"  Honest: {overall_averages['avg_honest_difference']:.3f}")
        print(f"  Dishonest: {overall_averages['avg_dishonest_difference']:.3f}")
        print(f"  Neutral: {overall_averages['avg_neutral_difference']:.3f}")
        print(f"Number of categories compared: {overall_averages['num_categories']}")

    if detailed:
        print("\n=== Detailed Model Comparisons ===")
        for category, comparisons in category_results.items():
            print(f"\n{category.upper()}:")
            for model, comp in comparisons.items():
                print(f"  {comp['file1_model']} vs {comp['file2_model']}:")
                print(
                    f"    File 1 - Honest: {comp['file1_honest']:.3f}, Dishonest: {comp['file1_dishonest']:.3f}, Neutral: {comp['file1_neutral']:.3f}"
                )
                print(
                    f"    File 2 - Honest: {comp['file2_honest']:.3f}, Dishonest: {comp['file2_dishonest']:.3f}, Neutral: {comp['file2_neutral']:.3f}"
                )
                print(
                    f"    Differences - Honest: {comp['honest_difference']:.3f}, Dishonest: {comp['dishonest_difference']:.3f}, Neutral: {comp['neutral_difference']:.3f}"
                )

    # Save to file if specified
    if output_file:
        with open(output_file, "w") as f:
            json.dump(output, f, indent=2)
        print(f"\nResults saved to: {output_file}")


if __name__ == "__main__":
    fire.Fire(compare_results)
