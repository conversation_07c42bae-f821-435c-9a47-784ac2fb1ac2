import pandas as pd
import glob
import os
import random
from openai import AsyncOpenAI
from anthropic import AsyncAnthropic
import argparse
import asyncio
from dotenv import load_dotenv
import torch
from vllm import LLM, SamplingParams
from transformers import AutoTokenizer
import math

load_dotenv()


def get_visible_gpus():
    """Get list of visible GPU indices."""
    if torch.cuda.is_available():
        return list(range(torch.cuda.device_count()))
    return []


def distribute_work_across_gpus(total_items, gpu_indices):
    """Distribute work items across available GPUs."""
    if not gpu_indices:
        return {0: list(range(total_items))}

    items_per_gpu = math.ceil(total_items / len(gpu_indices))
    distribution = {}

    for i, gpu_id in enumerate(gpu_indices):
        start_idx = i * items_per_gpu
        end_idx = min(start_idx + items_per_gpu, total_items)
        if start_idx < total_items:
            distribution[gpu_id] = list(range(start_idx, end_idx))

    return distribution


class VLLMClient:
    """VLLM client wrapper for Hugging Face models."""

    def __init__(
        self, model_name, gpu_memory_utilization=0.8, tensor_parallel_size=None
    ):
        self.model_name = model_name
        self.gpu_memory_utilization = gpu_memory_utilization

        # Auto-detect tensor parallel size if not specified
        if tensor_parallel_size is None:
            visible_gpus = get_visible_gpus()
            tensor_parallel_size = len(visible_gpus) if visible_gpus else 1

        # Initialize VLLM model
        self.llm = LLM(
            model=model_name,
            gpu_memory_utilization=gpu_memory_utilization,
            tensor_parallel_size=tensor_parallel_size,
            trust_remote_code=True,
        )

        # Load tokenizer for chat template
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)

    def generate_responses(
        self, messages_list, max_tokens=2000, temperature=0.7, enable_thinking=False
    ):
        """Generate responses for a batch of message lists."""
        # Apply chat template to all messages
        prompts = []
        for messages in messages_list:
            try:
                prompt = self.tokenizer.apply_chat_template(
                    messages,
                    tokenize=False,
                    add_generation_prompt=True,
                    enable_thinking=enable_thinking,
                )
                prompts.append(prompt)
            except Exception as e:
                print(f"Error applying chat template: {e}")
                prompts.append("")

        # Configure sampling parameters based on thinking mode
        if enable_thinking:
            # For thinking mode: Temperature=0.6, TopP=0.95, TopK=20
            sampling_params = SamplingParams(
                temperature=0.6, top_p=0.95, top_k=20, max_tokens=max_tokens
            )
        else:
            # For non-thinking mode: Temperature=0.7, TopP=0.8, TopK=20
            sampling_params = SamplingParams(
                temperature=0.7, top_p=0.8, top_k=20, max_tokens=max_tokens
            )

        # Generate responses
        outputs = self.llm.generate(prompts, sampling_params)

        # Extract generated text
        responses = []
        for output in outputs:
            if output.outputs:
                responses.append(output.outputs[0].text)
            else:
                responses.append("[Error: No output generated]")

        return responses


async def generate_responses_async(
    client,
    model_name,
    formatted_prompts,
    max_tokens,
    temperature,
    semaphore,
    K=1,
    enable_thinking=False,
):
    """
    Generates responses from the specified model for a batch of prompts.
    """
    results = {}

    # Check if this is a VLLM client (Hugging Face model)
    if isinstance(client, VLLMClient):
        # Handle VLLM generation
        for prompt_key, messages in formatted_prompts.items():
            try:
                responses = client.generate_responses(
                    [messages] * K,
                    max_tokens=max_tokens,
                    temperature=temperature,
                    enable_thinking=enable_thinking,
                )
                results[prompt_key] = responses
            except Exception as e:
                print(f"Error generating response for {prompt_key}: {e}")
                results[prompt_key] = [f"[Error: {str(e)}]"] * K

        return results

    # Original API-based generation code for OpenAI/Anthropic
    async def process_prompt(prompt_key, messages):
        """Processes a single prompt with retries and rate limiting."""
        retry_delay = 3
        max_retries = 7
        jitter = 0.1

        all_responses = []

        for _ in range(K):
            for attempt in range(max_retries):
                try:
                    async with semaphore:
                        # Model-specific API calls
                        if "claude" in model_name:
                            system_prompt = None
                            user_messages = messages.copy()
                            if messages and messages[0]["role"] == "system":
                                system_prompt = messages[0]["content"]
                                user_messages = messages[1:]

                            message = await client.messages.create(
                                model=model_name,
                                max_tokens=max_tokens,
                                system=system_prompt,
                                messages=user_messages,
                                temperature=temperature,
                            )
                        else:
                            if (
                                "gpt" in model_name
                                or "o1" in model_name
                                or "o3" in model_name
                            ):
                                # replace "system" with "developer"
                                if messages and messages[0]["role"] == "system":
                                    messages[0]["role"] = "developer"
                            completion = await client.chat.completions.create(
                                model=model_name,
                                messages=messages,
                                max_tokens=max_tokens,
                                temperature=temperature,
                            )

                    # Extract response based on model type
                    if "claude" in model_name:
                        response = message.content[0].text
                    else:
                        response = completion.choices[0].message.content

                    all_responses.append(response)
                    break

                except Exception as e:
                    if "invalid_prompt" in str(e):
                        print(
                            f"Prompt violated content policy for prompt: {prompt_key}. Replacing with error message."
                        )
                        all_responses.append(
                            "[Error: Prompt violated content policies]"
                        )
                        break
                    elif attempt == max_retries - 1:
                        print(f"Max retries exceeded for prompt: {prompt_key}")
                        print(e)
                        all_responses.append(None)
                    else:
                        print(
                            f"Error occurred for prompt: {prompt_key}. Attempt {attempt + 1}/{max_retries}. Retrying in {retry_delay:.1f} seconds."
                        )
                        print(e)
                        jittered_delay = retry_delay * (
                            1 + jitter * (2 * random.random() - 1)
                        )
                        await asyncio.sleep(jittered_delay)
                        retry_delay *= 2

        results[prompt_key] = all_responses

    tasks = [
        process_prompt(key, messages) for key, messages in formatted_prompts.items()
    ]
    await asyncio.gather(*tasks)

    return results


def create_model_client(model_name, gpu_memory_utilization=0.8):
    """Create appropriate client based on model type."""
    # Check if it's a Hugging Face model (contains '/' or is a known HF model)
    if (
        "/" in model_name
        or any(
            hf_org in model_name.lower()
            for hf_org in ["qwen", "llama", "mistral", "deepcogito", "meta-llama"]
        )
        or not any(
            api_model in model_name.lower()
            for api_model in ["gpt", "claude", "o1", "o3"]
        )
    ):

        print(f"Creating VLLM client for Hugging Face model: {model_name}")
        return VLLMClient(model_name, gpu_memory_utilization=gpu_memory_utilization)

    # API-based models
    elif "claude" in model_name.lower():
        api_key = os.getenv("ANTHROPIC_API_KEY")
        if not api_key:
            raise ValueError("ANTHROPIC_API_KEY not found in environment variables")
        return AsyncAnthropic(api_key=api_key)
    else:
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            raise ValueError("OPENAI_API_KEY not found in environment variables")
        return AsyncOpenAI(api_key=api_key)


async def process_dataframe(
    input_csv,
    output_csv,
    temperature,
    model_name="gpt-4o-mini",
    max_tokens=2000,
    belief_k=None,
    lie_k=10,
    enable_thinking=False,
    gpu_memory_utilization=0.8,
):
    """
    Process a dataframe and generate responses from an AI model.
    """
    client = None
    try:
        # Initialize model-specific client
        client = create_model_client(
            model_name, gpu_memory_utilization=gpu_memory_utilization
        )

        # For VLLM clients, we don't need semaphore; for API clients, we do
        semaphore = (
            asyncio.Semaphore(10) if not isinstance(client, VLLMClient) else None
        )

        # Read input data and set default system prompt if needed
        df = pd.read_csv(input_csv)
        if "belief_system_1" not in df.columns and "belief_elicit_1" in df.columns:
            df["belief_system_1"] = "You are a helpful assistant."

        # Check if this is a doubling down scenario (multi-turn conversation)
        is_doubling_down = (
            "dd_turn_1_prompt" in df.columns and "dd_turn_1_response" in df.columns
        )
        print(f"Is this a doubling down scenario? {is_doubling_down}")

        # Prepare prompts for generation
        all_prompts = []
        indices = []
        prompt_types = []

        # Process each row in the dataframe
        for index, row in df.iterrows():
            # Handle lying prompts - either doubling down or standard
            if is_doubling_down:
                # For doubling down, include the full conversation history
                for run in range(lie_k):
                    all_prompts.append(
                        [
                            {"role": "system", "content": row["system_prompt"]},
                            {"role": "user", "content": row["dd_turn_1_prompt"]},
                            {"role": "assistant", "content": row["dd_turn_1_response"]},
                            {"role": "user", "content": row["user_prompt"]},
                        ]
                    )
                    indices.append(index)
                    prompt_types.append(f"lying_run{run+1}")
            else:
                # Standard lying prompt with just system and user messages
                for run in range(lie_k):
                    all_prompts.append(
                        [
                            {"role": "system", "content": row["system_prompt"]},
                            {"role": "user", "content": row["user_prompt"]},
                        ]
                    )
                    indices.append(index)
                    prompt_types.append(f"lying_run{run+1}")

            # Handle belief elicitation prompts if present
            if "belief_elicit_1" in df.columns:
                for be_num, k_value in belief_k.items():
                    belief_col = f"belief_elicit_{be_num}"
                    if pd.notna(row.get(belief_col)):
                        for run in range(k_value):
                            all_prompts.append(
                                [
                                    {
                                        "role": "system",
                                        "content": row["belief_system_1"],
                                    },
                                    {"role": "user", "content": row[belief_col]},
                                ]
                            )
                            indices.append(index)
                            prompt_types.append(f"be{be_num}_run{run+1}")

        # For VLLM, distribute work across GPUs if there are multiple visible GPUs
        if isinstance(client, VLLMClient):
            visible_gpus = get_visible_gpus()
            print(f"Visible GPUs: {visible_gpus}")

            if len(visible_gpus) > 1:
                print(
                    f"Distributing {len(all_prompts)} prompts across {len(visible_gpus)} GPUs"
                )
                work_distribution = distribute_work_across_gpus(
                    len(all_prompts), visible_gpus
                )
                print(f"Work distribution: {work_distribution}")

        # Generate responses in parallel for all prompts
        if isinstance(client, VLLMClient):
            # For VLLM, generate all at once (it handles batching internally)
            all_responses = []
            for i, (prompt, prompt_type) in enumerate(zip(all_prompts, prompt_types)):
                response = await generate_responses_async(
                    client,
                    model_name,
                    {prompt_type: prompt},
                    max_tokens=max_tokens,
                    temperature=temperature,
                    semaphore=None,
                    K=1,
                    enable_thinking=enable_thinking,
                )
                all_responses.append(response)
        else:
            # For API clients, use the original async approach
            all_responses = await asyncio.gather(
                *[
                    generate_responses_async(
                        client,
                        model_name,
                        {prompt_type: prompt},
                        max_tokens=max_tokens,
                        temperature=temperature,
                        semaphore=semaphore,
                        K=1,
                        enable_thinking=enable_thinking,
                    )
                    for prompt, prompt_type in zip(all_prompts, prompt_types)
                ]
            )

        # Create mapping for output column names
        column_mapping = {
            f"lying_run{run+1}": f"generation(System Prompt + User Prompt)_run{run+1}"
            for run in range(lie_k)
        }

        # Add belief elicitation column names if present
        if "belief_elicit_1" in df.columns:
            for be_num, k_value in belief_k.items():
                for run in range(k_value):
                    column_mapping[f"be{be_num}_run{run+1}"] = (
                        f"generation(belief_system_1 + belief_elicit_{be_num})_run{run+1}"
                    )

        # Process responses and save to dataframe
        for response, index, prompt_type in zip(all_responses, indices, prompt_types):
            column_name = column_mapping[prompt_type]
            try:
                response_value = (
                    response[prompt_type][0] if prompt_type in response else None
                )
                if response_value is None:
                    raise ValueError("Generation failed or timed out")
                df.at[index, column_name] = response_value
            except Exception as e:
                error_msg = f"[ERROR: {str(e)}]"
                print(
                    f"WARNING: Error processing response for index {index}, prompt type {prompt_type}: {str(e)}"
                )
                df.at[index, column_name] = error_msg

        # Save results to CSV
        df.to_csv(output_csv, index=False)

    finally:
        # Clean up client if needed
        if hasattr(client, "close"):
            await client.close()


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Generate responses from AI models for a set of prompts"
    )
    parser.add_argument("--test", action="store_true", help="Run in test mode")
    parser.add_argument(
        "--model", default="gpt-4o", help="Model name to use for generation"
    )
    parser.add_argument(
        "--max_tokens", type=int, default=1000, help="Maximum tokens for generation"
    )
    parser.add_argument(
        "--temperature", type=float, default=1.0, help="Temperature for generation"
    )
    parser.add_argument(
        "--lie_k",
        type=int,
        default=1,
        help="Number of lying prompts to generate for each row",
    )
    parser.add_argument(
        "--thinking",
        action="store_true",
        help="Enable thinking mode for HuggingFace models",
    )
    parser.add_argument(
        "--gpu_memory_utilization",
        type=float,
        default=0.8,
        help="GPU memory utilization for VLLM (0.0-1.0)",
    )

    args = parser.parse_args()

    for file in glob.iglob("test_csv_data/*.csv" if args.test else "csv_data/*.csv"):
        belief_k = {"1": 3} if "statistics" in file else {"1": 3, "2": 1, "3": 1}

        modelname = args.model.split("/")[-1] if "/" in args.model else args.model
        if args.thinking:
            modelname += "_thinking"
        output_file = file.replace(".csv", f"_{modelname}.csv")
        output_file = output_file.replace("csv_data/", "csv_data/responses/")
        os.makedirs(os.path.dirname(output_file), exist_ok=True)

        # Skip if output file already exists
        if os.path.exists(output_file):
            print(f"Skipping {output_file} as it already exists")
            continue

        print("Processing", file, "to", output_file)
        asyncio.run(
            process_dataframe(
                file,
                output_file,
                temperature=args.temperature,
                model_name=args.model,
                max_tokens=args.max_tokens,
                belief_k=belief_k,
                lie_k=args.lie_k,
                enable_thinking=args.thinking,
                gpu_memory_utilization=args.gpu_memory_utilization,
            )
        )
