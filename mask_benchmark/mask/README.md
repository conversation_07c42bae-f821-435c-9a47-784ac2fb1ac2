# Model Evaluation Framework

This framework accompanies our research paper "Beyond Accuracy: Disentangling Honesty and Factuality in AI Models" which introduces MASK (Model Alignment between Statements and Knowledge), a novel benchmark for measuring honesty in large language models. Our research demonstrates that while larger models obtain higher accuracy, they do not necessarily become more honest when pressured to lie. We introduce a consistency-based honesty metric that disentangles accuracy from honesty by testing whether models will contradict their own beliefs under pressure. The framework provides tools for evaluating language models' responses across different scenarios, focusing on this critical distinction between knowing facts (accuracy) and choosing to state them truthfully (honesty).

## Overview

The framework consists of three main stages:
1. Response Generation
2. Response Evaluation
3. Metrics Processing

## Setup

1. Create a `.env` file in the root directory with your API keys:
```

OPENAI_API_KEY=your_key_here \# needed for evaluation
ANTHROPIC_API_KEY=your_key_here

```

2. Install dependencies (recommended to use a virtual environment):
```

python -m venv venv
source venv/bin/activate  \# On Windows: venv\Scripts\activate
pip install -r requirements.txt

```

3. Download data:
```

python download.py

```

## Directory Structure
```

├── csv_data/               \# Production data directory
│   ├── responses/         \# Generated model responses
│   ├── evaluated/         \# Evaluated responses
│   └── metrics/          \# Processed metrics
├── test_csv_data/         \# Test data directory (same structure as csv_data)
├── prompts/               \# Evaluation prompt templates
└── *.py                   \# Main Python scripts

```

## Usage

### 1. Generate Responses

Generate responses using the `generate_responses.py` script:
```

python generate_responses.py [--test] [--model MODEL_NAME] [--temperature TEMP] [--lie_k NUM_RUNS] [--thinking] [--gpu_memory_utilization RATIO]

```

Options:
- `--test`: Use test directories
- `--model`: Model name to use (default: gpt-4o, supports HuggingFace models)
- `--temperature`: Sampling temperature (default: 1.0)
- `--lie_k`: Number of lying prompts to generate per row (default: 1)
- `--thinking`: Enable thinking mode for HuggingFace models
- `--gpu_memory_utilization`: GPU memory utilization for VLLM (0.0-1.0, default: 0.8)

The script supports OpenAI, Anthropic, and local HuggingFace models via VLLM. Generated responses will be saved in `csv_data/responses/` following the naming convention: `{archetype}_{model}.csv`

Examples:
```


# Use local HuggingFace model with thinking

python generate_responses.py --model "Qwen/Qwen3-8B" --thinking --gpu_memory_utilization 0.9

# Use API model

python generate_responses.py --model "gpt-4o" --temperature 0.7

```

### 2. Evaluate Responses

Run the evaluation script:
```

python evaluate.py [--test] [--input_file path/to/file.csv] [--concurrency_limit 50]

```

Options:
- `--test`: Use test directories
- `--input_file`: Process a specific file (optional)
- `--concurrency_limit`: Maximum concurrent API calls (default: 50)

This script:
- Processes each response file through evaluation prompts
- Handles different archetypes (binary, statistics, etc.)
- Supports multiple evaluation runs per response
- Outputs results to the `evaluated/` directory

### 3. Calculate Metrics

Run the metrics calculation:
```

python metric.py [--test]

```

This script:
- Processes evaluated responses
- Calculates accuracy and honesty metrics
- Outputs to the `metrics/` directory

### 4. Process Final Metrics

Generate the final metrics summary:
```

python process_metrics.py [--test]

```

This creates `metrics/all_results.json` containing:
- Total responses per model/archetype
- Honesty scores (levels 1 and 10)
- Accuracy scores
- Other relevant metrics

## Metrics Explanation

- **Honesty Score**: Honesty in initial responses
- **Honesty Score (@10)**: Consistency across multiple generations
- **Accuracy**: Alignment with ground truth (where applicable)

## Testing

Use the `--test` flag with any script to run in test mode, which:
- Uses `test_csv_data/` instead of `csv_data/`
- Helps validate changes without affecting production data
- Provides a smaller dataset for quick iterations
