#!/bin/bash

# Pipeline script to process CSV files sequentially
# Usage: ./pipeline.sh <input_directory> [--test] [--concurrency_limit N] [--normalize]

set -e  # Exit on any error

# Check if input directory is provided
if [ $# -lt 1 ]; then
    echo "Usage: $0 <input_directory> [--test] [--concurrency_limit N] [--normalize]"
    echo "Example: $0 /path/to/csv_data"
    echo "Example: $0 /path/to/csv_data --test --concurrency_limit 30 --normalize"
    exit 1
fi

INPUT_DIR="$1"
shift  # Remove first argument so we can pass remaining args to scripts

# Check if input directory exists
if [ ! -d "$INPUT_DIR" ]; then
    echo "Error: Input directory '$INPUT_DIR' does not exist"
    exit 1
fi

# Check if responses subdirectory exists
RESPONSES_DIR="$INPUT_DIR/"
if [ ! -d "$RESPONSES_DIR" ]; then
    echo "Error: Responses directory '$RESPONSES_DIR' does not exist"
    exit 1
fi

# Find all CSV files in the responses directory
CSV_FILES=($(find "$RESPONSES_DIR" -name "*.csv" -type f))

if [ ${#CSV_FILES[@]} -eq 0 ]; then
    echo "Error: No CSV files found in '$RESPONSES_DIR'"
    exit 1
fi

echo "Found ${#CSV_FILES[@]} CSV files to process in '$RESPONSES_DIR'"

# Process each CSV file sequentially with evaluate.py
echo "=== Starting evaluation phase ==="
for csv_file in "${CSV_FILES[@]}"; do
    echo "Processing: $csv_file"

    # Run evaluate.py on the current file
    python evaluate.py --input_file "$csv_file" "$@"

    if [ $? -ne 0 ]; then
        echo "Error: Failed to process $csv_file"
        exit 1
    fi
done

echo "=== Evaluation phase completed ==="

# Run metric.py with the input directory
echo "=== Starting metrics calculation ==="
python metric.py --input_dir "$INPUT_DIR" "$@"

if [ $? -ne 0 ]; then
    echo "Error: Failed to run metric.py"
    exit 1
fi

echo "=== Metrics calculation completed ==="

# Run process_metrics.py with the input directory
echo "=== Starting metrics processing ==="
python process_metrics.py --input_dir "$INPUT_DIR" "$@"

if [ $? -ne 0 ]; then
    echo "Error: Failed to run process_metrics.py"
    exit 1
fi

echo "=== Pipeline completed successfully ==="
echo "Results saved in: $INPUT_DIR/metrics/"
